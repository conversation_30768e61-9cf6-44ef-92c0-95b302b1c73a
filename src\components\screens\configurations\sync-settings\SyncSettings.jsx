import React, { useEffect, useState } from "react";
import { Zap } from "lucide-react";
import LayoutWrapper from "../../../generic-components/LayoutWrapper";
import Authentication from "./section/Authentication";
import AccountingPlatform from "./section/AccountingPlatform";
import MISyncSettings from "./section/MISyncSettings";
import {
  getBusinessPreferences,
  getSyncStatus,
  getZohoConfigs,
} from "../../../services/syncSettingsServices";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "../../../../contexts/AuthContext";
import LoadingWrapper from "../../../global/components/LoadingWrapper";
import SyncStatus from "./section/SyncStatus";

const SyncSettings = () => {
  const { globSelectedBusiness } = useAuth();
  const [selectedPlatform, setSelectedPlatform] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const {
    data: businessPreferences,
    status,
    isLoading: businessPreferencesLoading,
  } = useQuery({
    queryKey: ["business-preferences", globSelectedBusiness?.business_id],
    queryFn: () => getBusinessPreferences(globSelectedBusiness?.business_id),
    refetchOnMount: true,
    retry: false,
    staleTime: 0,
    gcTime: 0,
  });
  const {
    data: zohoConfigs,
    status: zohoConfigsStatus,
    isLoading: zohoConfigsLoading,
  } = useQuery({
    queryKey: ["zoho-configs", globSelectedBusiness?.business_id],
    queryFn: () => getZohoConfigs(globSelectedBusiness?.business_id),
    refetchOnMount: true,
    enabled:
      businessPreferences?.platform_details?.platform_name?.toLowerCase() ===
      "zoho",
    retry: false,
    staleTime: 0,
    gcTime: 0,
  });
  const {
    data: syncData,
    isLoading: syncDataLoading,
    refetch: refetchSyncStatus,
  } = useQuery({
    queryKey: ["sync-status", globSelectedBusiness?.business_id],
    refetchOnMount: true,
    queryFn: () => getSyncStatus(globSelectedBusiness?.business_id),
    enabled: !!globSelectedBusiness?.business_id,
    retry: false,
    staleTime: 0,
    gcTime: 0,
  });

  useEffect(() => {
    if (status === "success") {
      setSelectedPlatform({
        id: businessPreferences?.platform_details?.id,
        platform_name: businessPreferences?.platform_details?.platform_name,
      });
    }
    if (status === "error") {
      setSelectedPlatform(null);
    }
  }, [status]);

  useEffect(() => {
    if (zohoConfigsStatus === "success") {
      setIsConnected(
        zohoConfigs?.status?.toLowerCase() === "connected" || false
      );
    }
    if (zohoConfigsStatus === "error") {
      setIsConnected(false);
    }
  }, [zohoConfigsStatus]);

  return (
    <LayoutWrapper>
      <LoadingWrapper
        loading={
          zohoConfigsLoading || businessPreferencesLoading || syncDataLoading
        }
      >
        <div className="max-h-screen overflow-y-auto bg-secondary-color">
          <div className="py-8 px-4 mx-auto">
            <div className="mb-8 -mt-8">
              <div className="flex items-center gap-3 mb-2">
                <div className="flex items-center justify-center w-6 h-6 bg-accent1-bg">
                  <Zap className="w-8 h-8 text-accent1" />
                </div>
                <label className="text-3xl font-bold leading-none text-primary-color">
                  Configuration Sync
                </label>
              </div>

              <p className="text-lg opacity-70 ml-9 text-primary-color">
                Streamline your accounting platform connectivity
              </p>
            </div>

            <AccountingPlatform
              selectedPlatform={selectedPlatform}
              setSelectedPlatform={setSelectedPlatform}
              isConnected={isConnected}
              refetchSyncStatus={refetchSyncStatus}
              syncProgress={syncData?.sync_progress || 100}
              didSyncFailed={syncData?.sync_status?.toLowerCase() === "failed"}
            />

            {selectedPlatform?.platform_name?.toLowerCase() === "zoho" && (
              <>
                <Authentication
                  setIsConnected={setIsConnected}
                  isConnected={isConnected}
                  businessPreferences={businessPreferences}
                  zohoConfigs={zohoConfigs}
                />

                <MISyncSettings
                  isConnected={isConnected}
                  businessPreferences={businessPreferences}
                />

                <SyncStatus syncData={syncData} />
              </>
            )}
          </div>
        </div>
      </LoadingWrapper>
    </LayoutWrapper>
  );
};

export default SyncSettings;
