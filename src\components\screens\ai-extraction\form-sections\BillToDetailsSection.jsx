import React, { useCallback } from "react";
import <PERSON>Field from "./components/AiField";
import { getFieldAlertObject } from "../../../../components/utils/aiUtils";
import CustomDatePicker from "../../../ui-components/fields/CustomDatePicker";
import useRealTimeValidation from "./hook/useRealTimeValidation";
import ModalDropdown from "./components/ModalDropdown";
import { useParams } from "react-router-dom";
import { resturls } from "../../../utils/apiurls";

const SECTION = "bill_to_details";

function BillToDetailsSection({
  formData,
  isReadOnly,
  invoiceType,
  formAction,
  setFormData,
}) {
  const { businessId } = useParams();
  const data = formData[SECTION] || {};
  const [handleValidate, suggestions] = useRealTimeValidation({
    formAction,
    data,
    defaultSuggestion: data?.recommended_fields?.[0],
    isInventoryEnabled: formData?.is_inventory_enabled,
  });

  const handleOnPaste = useCallback((copyText, field) => {
    setFormData((prevData) => {
      const prevSection = prevData[SECTION] || {};
      const updatedSection = { ...prevSection, [field]: copyText };
      handleValidate(SECTION, invoiceType, updatedSection);
      return {
        ...prevData,
        [SECTION]: updatedSection,
      };
    });
  }, []);

  return (
    <div className="form-grid">
      {/* Buyer GST No. */}
      <AiField
        label="Buyer's GST No."
        isExactMatch={data?.exact_match?.buyer_gst_no}
        alertObject={getFieldAlertObject(data, "buyer_gst_no")}
        type="text"
        name="buyer_gst_no"
        id="buyer_gst_no"
        value={data?.buyer_gst_no ?? ""}
        copyText={suggestions?.buyer_gst_no}
        onPaste={handleOnPaste}
        onBlur={() => handleValidate(SECTION, invoiceType)}
        onChange={(e) =>
          formAction("FIELD_CHANGE", SECTION, "buyer_gst_no", e.target.value)
        }
        disabled={isReadOnly}
      />

      {/* Cost Centre */}
      <div>
        <AiField
          label="Cost Center"
          isExactMatch={data?.exact_match?.cost_center_name}
          alertObject={getFieldAlertObject(data, "cost_center_name")}
          copyText={suggestions?.cost_center_name}
          onPaste={handleOnPaste}
          type="text"
          name="cost_center_name"
          id="cost_center_name"
          value={data?.cost_center_name ?? ""}
          readOnly
          onChange={(e) =>
            formAction(
              "FIELD_CHANGE",
              SECTION,
              "cost_center_name",
              e.target.value
            )
          }
          disabled={isReadOnly}
        />
        <ModalDropdown
          label="Select Cost Center"
          url={`${resturls.getCostCenter}?business_id=${businessId}`}
          searchParamName="search"
          onSelect={(option) => {
            formAction(
              "FIELD_CHANGE",
              SECTION,
              "cost_center_name",
              option.value
            );
          }}
          transformOptionsObj={{
            key: "master_id",
            value: "master_id",
            label: "cost_center_name",
          }}
          disabled={isReadOnly}
        />
      </div>

      {/* Invoice No. */}
      <AiField
        label="Invoice No."
        isExactMatch={data?.exact_match?.invoice_no}
        alertObject={getFieldAlertObject(data, "invoice_no")}
        required
        copyText={suggestions?.invoice_no}
        onPaste={(text) =>
          formAction("FIELD_CHANGE", SECTION, "invoice_no", text)
        }
        type="text"
        name="invoice_no"
        id="invoice_no"
        value={data?.invoice_no ?? ""}
        onChange={(e) =>
          formAction("FIELD_CHANGE", SECTION, "invoice_no", e.target.value)
        }
        disabled={isReadOnly}
      />

      {/* Invoice Date */}
      <AiField
        label="Invoice Date (dd/mm/yyyy)"
        isExactMatch={data?.exact_match?.invoice_date}
        alertObject={getFieldAlertObject(data, "invoice_date")}
        copyText={suggestions?.invoice_date}
        onPaste={(text) =>
          formAction("FIELD_CHANGE", SECTION, "invoice_date", text)
        }
        id="invoice_date"
        renderCustomField={() => (
          <CustomDatePicker
            selected={data?.invoice_date}
            onDateChange={(date) => {
              const latestSectionData = formAction(
                "FIELD_CHANGE",
                SECTION,
                "invoice_date",
                date
              );
              handleValidate(SECTION, invoiceType, latestSectionData);
            }}
            name="invoice_date"
            id="invoice_date"
            className="input-field"
            disabled={isReadOnly}
            portalId={"date-picker-portal"}
          />
        )}
      />

      {/* Buyer Name */}
      <AiField
        label="Buyer's Name"
        isExactMatch={data?.exact_match?.buyer_name}
        alertObject={getFieldAlertObject(data, "buyer_name")}
        required
        copyText={suggestions?.buyer_name}
        onPaste={handleOnPaste}
        onBlur={() => handleValidate(SECTION, invoiceType)}
        className="only-1-column"
        type="text"
        name="buyer_name"
        id="buyer_name"
        value={data?.buyer_name ?? ""}
        onChange={(e) =>
          formAction("FIELD_CHANGE", SECTION, "buyer_name", e.target.value)
        }
        disabled={isReadOnly}
      />

      {/* Buyer Address */}
      <AiField
        label="Buyer's Address"
        isExactMatch={data?.exact_match?.buyer_address}
        alertObject={getFieldAlertObject(data, "buyer_address")}
        className="only-1-column"
        copyText={suggestions?.buyer_address}
        onPaste={(text) =>
          formAction("FIELD_CHANGE", SECTION, "buyer_address", text)
        }
        Element="textarea"
        name="buyer_address"
        id="buyer_address"
        value={data?.buyer_address ?? ""}
        onChange={(e) =>
          formAction("FIELD_CHANGE", SECTION, "buyer_address", e.target.value)
        }
        disabled={isReadOnly}
      />

      {/* Three fields in one row */}
      <div className="only-3-column">
        {/* State Name */}
        <AiField
          label="Buyer's State Name"
          isExactMatch={data?.exact_match?.buyer_state_name}
          alertObject={getFieldAlertObject(data, "buyer_state_name")}
          copyText={suggestions?.buyer_state_name}
          onPaste={handleOnPaste}
          onBlur={() => handleValidate(SECTION, invoiceType)}
          type="text"
          name="buyer_state_name"
          id="buyer_state_name"
          value={data?.buyer_state_name ?? ""}
          onChange={(e) =>
            formAction(
              "FIELD_CHANGE",
              SECTION,
              "buyer_state_name",
              e.target.value
            )
          }
          disabled={isReadOnly}
        />

        {/* Buyer State Code */}
        <AiField
          label="Buyer's State Code"
          isExactMatch={data?.exact_match?.buyer_state_code}
          alertObject={getFieldAlertObject(data, "buyer_state_code")}
          copyText={suggestions?.buyer_state_code}
          onPaste={handleOnPaste}
          onBlur={() => handleValidate(SECTION, invoiceType)}
          type="text"
          name="buyer_state_code"
          id="buyer_state_code"
          value={data?.buyer_state_code ?? ""}
          onChange={(e) =>
            formAction(
              "FIELD_CHANGE",
              SECTION,
              "buyer_state_code",
              e.target.value
            )
          }
          disabled={isReadOnly}
        />

        {/* Buyer PAN No. */}
        <AiField
          label="Buyer's PAN No."
          isExactMatch={data?.exact_match?.buyer_pan_no}
          alertObject={getFieldAlertObject(data, "buyer_pan_no")}
          copyText={suggestions?.buyer_pan_no}
          onPaste={handleOnPaste}
          onBlur={() => handleValidate(SECTION, invoiceType)}
          type="text"
          name="buyer_pan_no"
          id="buyer_pan_no"
          value={data?.buyer_pan_no ?? ""}
          onChange={(e) =>
            formAction("FIELD_CHANGE", SECTION, "buyer_pan_no", e.target.value)
          }
          disabled={isReadOnly}
        />
      </div>
    </div>
  );
}

export default BillToDetailsSection;
