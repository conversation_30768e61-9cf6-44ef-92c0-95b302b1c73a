import { Clock } from "lucide-react";
import React, { useMemo } from "react";
import { formatRelativeTime } from "../../../../utils";

function SyncStatus({ syncData }) {
  const syncItems = useMemo(
    () => [
      {
        id: "vendors",
        name: "Vendors",
        status: syncData?.contacts?.status,
        lastSyncedAt: syncData?.contacts?.last_synced_at,
      },
      {
        id: "chart_of_accounts",
        name: "Chart of Accounts",
        status: syncData?.chart_of_accounts?.status,
        lastSyncedAt: syncData?.chart_of_accounts?.last_synced_at,
      },
      {
        id: "tax",
        name: "Tax",
        status: syncData?.tax?.status,
        lastSyncedAt: syncData?.tax?.last_synced_at,
      },
      {
        id: "items",
        name: "Items",
        status: syncData?.stock_item?.status,
        lastSyncedAt: syncData?.stock_item?.last_synced_at,
      },
    ],
    [syncData]
  );

  return (
    <div className="p-6 rounded-2xl shadow-lg bg-white border border-accent1-border">
      <div className="flex items-center gap-3 mb-4 mt-2">
        <div className="flex items-center justify-center rounded w-6 h-6">
          <Clock className="w-6 h-6 text-accent1" />
        </div>
        <label className="text-xl font-bold leading-none text-primary-color">
          Sync Status
        </label>
      </div>

      <div className="mb-6 p-3 rounded-lg w-fit ml-3 bg-accent1-bg">
        <p className="text-lg font-medium">
          Last Sync:{" "}
          <span className="text-accent1">
            {syncData?.end_time
              ? formatRelativeTime(syncData.end_time)
              : "Never"}
          </span>
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4 max-w-md ml-3">
        {syncItems.map((item) => (
          <div
            key={item.id}
            className="flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:shadow-md bg-accent1-bg"
          >
            <div className="flex flex-col">
              <span className="text-lg font-medium">{item.name}</span>
              {item.lastSyncedAt && (
                <span className="text-sm text-gray-600">
                  Last synced: {formatRelativeTime(item.lastSyncedAt)}
                </span>
              )}
            </div>

            <div className="flex items-center gap-2">
              {item.status === "failed" && (
                <span className="text-lg px-2 py-1 rounded-full bg-error-bg text-error">
                  Sync Failed
                </span>
              )}
              <div
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  item.status === "completed"
                    ? "bg-success"
                    : item.status === "failed"
                    ? "bg-error"
                    : "bg-warning"
                }`}
              ></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default SyncStatus;
