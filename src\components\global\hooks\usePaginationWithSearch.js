import { useState, useEffect, useCallback } from "react";
import debounce from "lodash/debounce";
import apiClient from "../../services/apiClient";
import { Pagination } from "semantic-ui-react";
import useUpdateEffect from "./useUpdateEffect";
import { useSearchParams } from "react-router-dom";

const usePaginationWithSearch = ({
  url,
  queryParam = "search",
  initialPage = 1,
  debounceDelay = 600,
  pageCount = 10,
  shouldDoInitialFetch = true,
  skipFetching = false,
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const currentPage = searchParams.get("page") || initialPage;
  const [query, setQuery] = useState(
    decodeURIComponent(searchParams.get(queryParam) || "")
  );
  const [totalItemsCount, setTotalItemsCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(!!url && shouldDoInitialFetch);
  const [error, setError] = useState(null);
  const [extraParams, setExtraParams] = useState({});

  const fetchData = useCallback(
    debounce(
      async (page, searchTerm, otherParams, signal, skipLoading = false) => {
        if (!url) return;
        !skipLoading && setLoading(true);
        setError(null);

        try {
          const filteredParams = Object.fromEntries(
            Object.entries({
              page,
              ...otherParams,
              [queryParam]: searchTerm,
            }).filter(([, value]) => !!value)
          );
          const response = await apiClient.get(url, {
            params: filteredParams,
            signal,
          });

          const { count, results } = response;
          setTotalItemsCount(count);
          setTotalPages(Math.ceil(count / pageCount));
          setData(results);
        } catch (err) {
          if (!signal.aborted) {
            setError(err.message);
          }
        } finally {
          !skipLoading && setLoading(false);
        }
      },
      debounceDelay
    ),
    [url, pageCount]
  );

  useUpdateEffect(() => {
    const page = query ? 1 : currentPage;
    if (!skipFetching) {
      const controller = new AbortController();
      fetchData(page, query, extraParams, controller.signal);
      return () => controller.abort();
    }
  }, [query, extraParams, currentPage, url]);

  //Only for when mounts
  useEffect(() => {
    if (shouldDoInitialFetch && !skipFetching) {
      const controller = new AbortController();
      fetchData(currentPage, query, extraParams, controller.signal);
      return () => controller.abort();
    }
  }, []);

  const handlePageChange = (page) => {
    if (page < 1 || page > totalPages) return;
    setSearchParams({ page: String(page) });
  };

  const handleQueryChange = (newQuery) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      if (newQuery) {
        newParams.set(queryParam, newQuery);
      } else {
        newParams.delete(queryParam);
      }
      return newParams;
    });
    setQuery(decodeURIComponent(newQuery));
  };

  return {
    data,
    setData,
    totalItemsCount,
    currentPage,
    totalPages,
    loading,
    error,
    setPage: handlePageChange,
    query,
    setQuery: handleQueryChange,
    setExtraParams,
    refetch: (skipLoading = true) => {
      const controller = new AbortController();
      fetchData(
        currentPage,
        query,
        extraParams,
        controller.signal,
        skipLoading
      );
      return () => controller.abort();
    },
    PaginationComponent: totalPages > 1 && (
      <Pagination
        activePage={currentPage}
        totalPages={totalPages}
        onPageChange={(_, { activePage }) => handlePageChange(activePage)}
      />
    ),
  };
};

export default usePaginationWithSearch;
