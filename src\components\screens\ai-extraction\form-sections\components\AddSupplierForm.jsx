import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  IconButton,
  Autocomplete,
} from "@mui/material";
import { Formik, Field, FieldArray } from "formik";
import * as yup from "yup";
import { ExpandMore, Add, Delete } from "@mui/icons-material";
import { INDIAN_STATES } from "../../../../utils/constants";
import { addZohoSupplier } from "../../../../services/aiServices";
import { useAuth } from "../../../../../contexts/AuthContext";
import { getErrorMessage } from "../../../../utils/apiUtils";
import { toast } from "react-toastify";

// GST treatment options
const GST_TREATMENT_OPTIONS = [
  {
    key: "registered_business",
    text: "Registered Business",
    value: "registered_business",
  },
  {
    key: "unregistered_business",
    text: "Unregistered Business",
    value: "unregistered_business",
  },
  {
    key: "consumer",
    text: "Consumer",
    value: "consumer",
  },
  {
    key: "overseas",
    text: "Overseas",
    value: "overseas",
  },
  {
    key: "special_economic_zone",
    text: "Special Economic Zone",
    value: "special_economic_zone",
  },
  {
    key: "deemed_export",
    text: "Deemed Export",
    value: "deemed_export",
  },
  {
    key: "business_none",
    text: "Business (None)",
    value: "business_none",
  },
];

// Vendor type options
const VENDOR_TYPE_OPTIONS = [
  { key: "business", text: "Business", value: "business" },
  { key: "individual", text: "Individual", value: "individual" },
];

// Yup validation schema
const validationSchema = yup.object().shape({
  contact_name: yup.string().required("Contact name is required"),
  vendor_type: yup.string().required("Vendor type is required"),
  gst_treatment: yup.string().required("GST treatment is required"),
  place_of_contact: yup.string().required("Place of contact is required"),
  contact_persons: yup.array().of(
    yup.object().shape({
      first_name: yup.string(),
      last_name: yup.string(),
      email: yup.string().email("Invalid email format"),
      phone: yup.string().matches(/^[0-9]{10}$/, "Phone must be 10 digits"),
    })
  ),
  billing_address: yup.object().shape({
    address: yup.string(),
    city: yup.string(),
    state: yup.string(),
    zip: yup.string(),
    country: yup.string(),
  }),
  shipping_address: yup.object().shape({
    address: yup.string(),
    city: yup.string(),
    state: yup.string(),
    zip: yup.string(),
    country: yup.string(),
  }),
});

function AddSupplierForm({ formData, open, onClose, onSave = null }) {
  const { globSelectedBusiness } = useAuth();

  const [expandedSections, setExpandedSections] = useState({
    contactPersons: false,
    billingAddress: false,
    shippingAddress: false,
  });

  const initialValues = useMemo(() => {
    return {
      contact_name: formData?.supplier_details?.supplier_name,
      company_name: formData?.supplier_details?.supplier_name,
      contact_type: "vendor", // handled internally
      vendor_type: "business",
      contact_persons: [],
      billing_address: {
        address: "",
        city: "",
        state: "",
        zip: "",
        country: "",
      },
      shipping_address: {
        address: "",
        city: "",
        state: "",
        zip: "",
        country: "",
      },
      gst_treatment: "registered_business",
      place_of_contact: "",
      payment_terms: formData?.other_information?.payment_terms,
    };
  }, []);

  const handleSectionToggle = useCallback((section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  }, []);

  const handleFormSubmit = useCallback((values, { resetForm }) => {
    addZohoSupplier(globSelectedBusiness?.business_id, values)
      .then(() => {
        onSave?.(values);
        resetForm();
        onClose?.();
        toast.success("Supplier added successfully");
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        toast.error(errorMessage);
      });
  }, []);

  return (
    <Dialog open={open} onClose={() => onClose?.()} maxWidth="lg" fullWidth>
      <DialogTitle>Add New Supplier</DialogTitle>
      <DialogContent className="!pt-3">
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleFormSubmit}
          enableReinitialize
          validateOnMount={true}
        >
          {({ values, errors, touched, handleSubmit }) => {
            return (
              <>
                <form
                  onSubmit={handleSubmit}
                  id="add-supplier-form"
                  className="relative"
                >
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <Field name="contact_name">
                      {({ field }) => (
                        <TextField
                          {...field}
                          label="Contact Name"
                          placeholder="Enter supplier name"
                          fullWidth
                          required
                          error={
                            !!(errors.contact_name && touched.contact_name)
                          }
                          size="small"
                        />
                      )}
                    </Field>

                    <Field name="company_name">
                      {({ field }) => (
                        <TextField
                          {...field}
                          label="Company Name"
                          placeholder="Enter company name (optional)"
                          fullWidth
                          size="small"
                        />
                      )}
                    </Field>

                    <SelectFormikField
                      name="vendor_type"
                      label="Vendor Type"
                      options={VENDOR_TYPE_OPTIONS}
                      isError={!!(errors.vendor_type && touched.vendor_type)}
                      required
                    />

                    <SelectFormikField
                      name="gst_treatment"
                      label="GST Treatment"
                      options={GST_TREATMENT_OPTIONS}
                      isError={
                        !!(errors.gst_treatment && touched.gst_treatment)
                      }
                      required
                    />

                    <Field name="place_of_contact">
                      {({ field, form }) => {
                        const selectedOption =
                          INDIAN_STATES.find(
                            (option) => option.code === field.value
                          ) || null;
                        return (
                          <Autocomplete
                            name={field.name}
                            value={selectedOption}
                            onChange={(_, newValue) => {
                              form.setFieldValue(
                                field.name,
                                newValue ? newValue.code : ""
                              );
                            }}
                            onBlur={() =>
                              form.setFieldTouched(field.name, true)
                            }
                            options={INDIAN_STATES}
                            getOptionLabel={(option) => option.name || ""}
                            openOnFocus
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                error={
                                  !!(
                                    form.errors.place_of_contact &&
                                    form.touched.place_of_contact
                                  )
                                }
                                required
                                label="Place of Contact"
                                size="small"
                                fullWidth
                              />
                            )}
                          />
                        );
                      }}
                    </Field>

                    <Field name="payment_terms">
                      {({ field }) => (
                        <TextField
                          {...field}
                          label="Payment Terms (Days)"
                          placeholder="Enter payment terms"
                          type="number"
                          fullWidth
                          size="small"
                        />
                      )}
                    </Field>
                  </div>

                  {/* Contact Persons Section */}
                  <Accordion
                    expanded={expandedSections.contactPersons}
                    onChange={() => handleSectionToggle("contactPersons")}
                    className="mb-4"
                  >
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography variant="subtitle1" className="font-medium">
                        Contact Persons (Optional)
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <FieldArray name="contact_persons">
                        {({ push, remove }) => (
                          <div>
                            {values.contact_persons.map((person, index) => (
                              <div
                                key={index}
                                className="border rounded-lg p-4 mb-4 bg-gray-50"
                              >
                                <div className="flex justify-between items-center mb-3">
                                  <Typography variant="subtitle2">
                                    Contact Person {index + 1}
                                  </Typography>
                                  <IconButton
                                    onClick={() => remove(index)}
                                    size="small"
                                    color="error"
                                  >
                                    <Delete />
                                  </IconButton>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                  <Field
                                    name={`contact_persons.${index}.first_name`}
                                  >
                                    {({ field }) => (
                                      <TextField
                                        {...field}
                                        label="First Name"
                                        placeholder="Enter first name"
                                        fullWidth
                                        size="small"
                                      />
                                    )}
                                  </Field>

                                  <Field
                                    name={`contact_persons.${index}.last_name`}
                                  >
                                    {({ field }) => (
                                      <TextField
                                        {...field}
                                        label="Last Name"
                                        placeholder="Enter last name"
                                        fullWidth
                                        size="small"
                                      />
                                    )}
                                  </Field>

                                  <Field
                                    name={`contact_persons.${index}.email`}
                                  >
                                    {({ field }) => (
                                      <TextField
                                        {...field}
                                        label="Email"
                                        placeholder="Enter email"
                                        type="email"
                                        fullWidth
                                        size="small"
                                        error={
                                          !!(
                                            errors.contact_persons?.[index]
                                              ?.email &&
                                            touched.contact_persons?.[index]
                                              ?.email
                                          )
                                        }
                                      />
                                    )}
                                  </Field>

                                  <Field
                                    name={`contact_persons.${index}.phone`}
                                  >
                                    {({ field }) => (
                                      <TextField
                                        {...field}
                                        label="Phone"
                                        placeholder="Enter 10-digit phone number"
                                        fullWidth
                                        size="small"
                                        error={
                                          !!(
                                            errors.contact_persons?.[index]
                                              ?.phone &&
                                            touched.contact_persons?.[index]
                                              ?.phone
                                          )
                                        }
                                      />
                                    )}
                                  </Field>
                                </div>
                              </div>
                            ))}
                            <Button
                              startIcon={<Add />}
                              onClick={() =>
                                push({
                                  first_name: "",
                                  last_name: "",
                                  email: "",
                                  phone: "",
                                })
                              }
                              variant="outlined"
                              size="small"
                            >
                              Add Contact Person
                            </Button>
                          </div>
                        )}
                      </FieldArray>
                    </AccordionDetails>
                  </Accordion>

                  {/* Billing Address Section */}
                  <Accordion
                    expanded={expandedSections.billingAddress}
                    onChange={() => handleSectionToggle("billingAddress")}
                    className="mb-4"
                  >
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography variant="subtitle1" className="font-medium">
                        Billing Address (Optional)
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                          <Field name="billing_address.address">
                            {({ field }) => (
                              <TextField
                                {...field}
                                label="Address"
                                placeholder="Enter billing address"
                                fullWidth
                                multiline
                                rows={2}
                                size="small"
                              />
                            )}
                          </Field>
                        </div>

                        <Field name="billing_address.city">
                          {({ field }) => (
                            <TextField
                              {...field}
                              label="City"
                              placeholder="Enter city"
                              fullWidth
                              size="small"
                            />
                          )}
                        </Field>

                        <Field name="billing_address.state">
                          {({ field }) => (
                            <TextField
                              {...field}
                              label="State"
                              placeholder="Enter state"
                              fullWidth
                              size="small"
                            />
                          )}
                        </Field>

                        <Field name="billing_address.zip">
                          {({ field }) => (
                            <TextField
                              {...field}
                              label="ZIP Code"
                              placeholder="Enter ZIP code"
                              fullWidth
                              size="small"
                            />
                          )}
                        </Field>

                        <Field name="billing_address.country">
                          {({ field }) => (
                            <TextField
                              {...field}
                              label="Country"
                              placeholder="Enter country"
                              fullWidth
                              size="small"
                            />
                          )}
                        </Field>
                      </div>
                    </AccordionDetails>
                  </Accordion>

                  {/* Shipping Address Section */}
                  <Accordion
                    expanded={expandedSections.shippingAddress}
                    onChange={() => handleSectionToggle("shippingAddress")}
                    className="mb-4"
                  >
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography variant="subtitle1" className="font-medium">
                        Shipping Address (Optional)
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                          <Field name="shipping_address.address">
                            {({ field }) => (
                              <TextField
                                {...field}
                                label="Address"
                                placeholder="Enter shipping address"
                                fullWidth
                                multiline
                                rows={2}
                                size="small"
                              />
                            )}
                          </Field>
                        </div>

                        <Field name="shipping_address.city">
                          {({ field }) => (
                            <TextField
                              {...field}
                              label="City"
                              placeholder="Enter city"
                              fullWidth
                              size="small"
                            />
                          )}
                        </Field>

                        <Field name="shipping_address.state">
                          {({ field }) => (
                            <TextField
                              {...field}
                              label="State"
                              placeholder="Enter state"
                              fullWidth
                              size="small"
                            />
                          )}
                        </Field>

                        <Field name="shipping_address.zip">
                          {({ field }) => (
                            <TextField
                              {...field}
                              label="ZIP Code"
                              placeholder="Enter ZIP code"
                              fullWidth
                              size="small"
                            />
                          )}
                        </Field>

                        <Field name="shipping_address.country">
                          {({ field }) => (
                            <TextField
                              {...field}
                              label="Country"
                              placeholder="Enter country"
                              fullWidth
                              size="small"
                            />
                          )}
                        </Field>
                      </div>
                    </AccordionDetails>
                  </Accordion>
                </form>

                <DialogActions>
                  <Button onClick={() => onClose?.()} color="inherit">
                    Cancel
                  </Button>
                  <Button
                    onClick={() => handleSubmit()}
                    variant="contained"
                    className="!bg-accent2 !text-white hover:!bg-accent2-hover"
                    type="submit"
                  >
                    Add Supplier
                  </Button>
                </DialogActions>
              </>
            );
          }}
        </Formik>
      </DialogContent>
    </Dialog>
  );
}

const SelectFormikField = ({
  name,
  label,
  options,
  isError,
  required,
  transformValue = { key: "key", value: "value", text: "text" },
  ...props
}) => {
  return (
    <Field name={name}>
      {({ field }) => (
        <FormControl fullWidth error={isError} size="small" required={required}>
          <InputLabel>{label}</InputLabel>
          <Select {...field} label={label} {...props}>
            {options.map((option) => (
              <MenuItem
                key={option[transformValue.key]}
                value={option[transformValue.value]}
              >
                {option[transformValue?.text || transformValue?.value]}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      )}
    </Field>
  );
};

export default React.memo(AddSupplierForm);
