import { Check, Loader2, X } from "lucide-react";
import React, { useState } from "react";
import {
  connectToZoho,
  zohoCallback,
} from "../../../../../services/syncSettingsServices";
import { useAuth } from "../../../../../../contexts/AuthContext";
import { toast } from "react-toastify";
import { getErrorMessage } from "../../../../../utils/apiUtils";
import { authPopup } from "../../../../../utils/authUtils";

function ConnectToZohoBtn({
  setIsConnected,
  isConnected,
  organizationId,
  region,
}) {
  const [isConnecting, setIsConnecting] = useState(false);
  const { globSelectedBusiness } = useAuth();
  const handleConnect = () => {
    setIsConnecting(true);
    const redirectUrl = window.location.href;
    connectToZoho(globSelectedBusiness?.business_id, organizationId, region)
      .then((res) => {
        const authUrl = res?.auth_url;
        if (!authUrl) throw new Error("No auth URL provided");
        authPopup(authUrl, redirectUrl, setIsConnecting)
          .then((res) => {
            const restAuthParams = res?.split("?")[1];
            if (!restAuthParams) throw new Error("No auth params provided");
            zohoCallback(globSelectedBusiness?.business_id, restAuthParams)
              .then(() => {
                setIsConnected(true);
                setIsConnecting(false);
                toast.success("Zoho connected successfully!");
              })
              .catch((err) => {
                const error = getErrorMessage(err);
                toast.error(error);
                setIsConnecting(false);
              });
          })
          .catch((err) => {
            const error = getErrorMessage(err);
            toast.error(error);
            setIsConnecting(false);
          });
      })
      .catch((err) => {
        const error = getErrorMessage(err);
        toast.error(error);
        setIsConnecting(false);
      });
  };

  return (
    <div className="flex flex-wrap items-center gap-6 ml-3">
      <button
        onClick={handleConnect}
        disabled={isConnecting || !organizationId}
        className={`flex items-center justify-center py-[0.8em] px-[0.8em] rounded-xl transition-colors duration-200 whitespace-nowrap select-none common-btn-schema gap-2 ${
          isConnecting || !organizationId
            ? "opacity-50 cursor-not-allowed"
            : "hover:opacity-90"
        }`}
        data-tooltip-id="tooltip"
        data-tooltip-content={
          !organizationId ? "Please enter organization ID" : ""
        }
      >
        {isConnecting ? <Loader2 className="w-5 h-5 animate-spin" /> : null}
        {isConnecting ? "Connecting..." : "Connect to Zoho"}
      </button>

      <div
        className={`flex items-center gap-3 px-4 py-2 rounded-lg ${
          isConnected ? "bg-success-bg" : "bg-error-bg"
        }`}
      >
        <span className="text-lg font-medium text-primary-color">Status:</span>
        <div className="flex items-center gap-2">
          {isConnected ? (
            <Check className="w-5 h-5 text-success" />
          ) : (
            <X className="w-5 h-5 text-error" />
          )}
          <span
            className={`font-semibold ${
              isConnected ? "text-success" : "text-error"
            }`}
          >
            {isConnected ? "Connected" : "Not Connected"}
          </span>
        </div>
      </div>
    </div>
  );
}

export default ConnectToZohoBtn;
