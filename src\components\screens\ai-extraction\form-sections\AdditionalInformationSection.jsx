import React, { useEffect, useState } from "react";
import { Select, MenuItem, FormControl } from "@mui/material";
import <PERSON>Field from "./components/AiField";
import { getFieldAlertObject } from "../../../../components/utils/aiUtils";
import { CREATION_BUSINESS_ID } from "../../../utils/constants";

const SECTION = "additional_information";

function AdditionalInformationSection({ formData, isReadOnly, formAction }) {
  const data = formData[SECTION] || {};
  const [selectedCutType, setSelectedCutType] = useState(() => {
    if (data?.cut_type === true) return "cut";
    if (data?.cut_type === false) return "roll";
    return "";
  });

  useEffect(() => {
    if (selectedCutType === null || selectedCutType === "") {
      formAction("FIELD_CHANGE", SECTION, "cut_type", "");
    } else {
      const isCutType = selectedCutType === "cut";
      formAction("FIELD_CHANGE", SECTION, "cut_type", isCutType);
    }
  }, [selectedCutType]);

  return (
    <div className="form-grid">
      {formData?.business_id === CREATION_BUSINESS_ID && (
        <AiField
          label="Type"
          isExactMatch={data?.exact_match?.type}
          alertObject={getFieldAlertObject(data, "type")}
          className="only-1-column"
          required
          renderCustomField={() => (
            <FormControl fullWidth>
              <Select
                value={selectedCutType}
                onChange={(e) => setSelectedCutType(e.target.value)}
                disabled={isReadOnly}
                displayEmpty
                className="input-field"
                size="small"
              >
                <MenuItem value="">
                  <em>Select Type</em>
                </MenuItem>
                <MenuItem value="cut">Cut</MenuItem>
                <MenuItem value="roll">Roll</MenuItem>
              </Select>
            </FormControl>
          )}
        />
      )}

      <AiField
        label="Remarks"
        isExactMatch={data?.exact_match?.remarks}
        alertObject={getFieldAlertObject(data, "remarks")}
        Element="textarea"
        name="remarks"
        id="remarks"
        value={data?.remarks ?? ""}
        className="only-1-column"
        onChange={(e) =>
          formAction("FIELD_CHANGE", SECTION, "remarks", e.target.value)
        }
        disabled={isReadOnly}
        rows={4}
      />
    </div>
  );
}

export default AdditionalInformationSection;
