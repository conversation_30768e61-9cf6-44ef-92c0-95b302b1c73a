import React, { useCallback, useMemo, useState } from "react";
import { useAuth } from "../../../contexts/AuthContext";
import { FileText, Plus, Upload, CheckCircle } from "lucide-react";
import { toast } from "react-toastify";
import FilePreview2 from "../../custom-components/FilePreview2";
import uploadService from "../../services/uploadService";
import useDragAndDrop from "../../global/hooks/useDragAndDrop";
import { createTicket } from "../../services/ticketServices";
import { getCategoryIdByName } from "../../utils/helperUtils";
import LayoutWrapper from "../../generic-components/LayoutWrapper";

const purchaseExpenseCategory = await getCategoryIdByName([
  { category: "purchase", subCategory: "invoice" },
  { category: "expense" },
]);

function InvoicesCreation() {
  const [files, setFiles] = useState([]);
  const [invoiceType, setInvoiceType] = useState("");
  const { globSelectedBusiness, userInfo } = useAuth();

  const isAllFilesUploaded = useMemo(() => {
    if (files.length === 0) return false;
    return files.every((file) => file.status === "success");
  }, [files]);

  const handleUploadFile = useCallback(async (file) => {
    setFiles((prev) =>
      prev.map((f) => (f.id === file.id ? { ...f, status: "uploading" } : f))
    );
    uploadService
      .uploadFile("/api/file/", file.file, (progress) => {
        setFiles((prev) =>
          prev.map((f) => (f.id === file.id ? { ...f, progress } : f))
        );
      })
      .then((response) => {
        setFiles((prev) =>
          prev.map((f) =>
            f.id === file.id
              ? { ...f, status: "success", responseId: response.id }
              : f
          )
        );
      })
      .catch(() => {
        setFiles((prev) =>
          prev.map((f) => (f.id === file.id ? { ...f, status: "error" } : f))
        );
      });
  }, []);

  const handleFiles = useCallback(
    (selectedFiles) => {
      const filesWithIds = selectedFiles.reduce((acc, file) => {
        const fileType = file.type;
        if (
          fileType === "application/pdf" ||
          fileType === "image/jpeg" ||
          fileType === "image/png"
        ) {
          const id = crypto.randomUUID();
          const fileObj = {
            file: file,
            type: file.type,
            name: file.name,
            progress: 0,
            id,
            previewUrl: URL.createObjectURL(file),
          };
          acc.push(fileObj);
        }
        return acc;
      }, []);

      if (filesWithIds.length !== selectedFiles.length) {
        toast.error(
          "Only PDF and image files are allowed (removed invalid files)"
        );
      }

      setFiles((prevFiles) => [...prevFiles, ...filesWithIds]);
      filesWithIds.forEach((file) => handleUploadFile(file));
    },
    [handleUploadFile]
  );

  const { isDragActive, eventHandlers } = useDragAndDrop(handleFiles);

  const handleRetryUpload = useCallback(
    (id) => {
      const fileToRetry = files.find((f) => f.id === id);
      if (fileToRetry) {
        handleUploadFile(fileToRetry);
      }
    },
    [files, handleUploadFile]
  );

  const handleSubmit = useCallback(async () => {
    if (files.length === 0 || invoiceType === "") return;
    const data = {
      category:
        invoiceType === "purchase"
          ? purchaseExpenseCategory[0].category
          : purchaseExpenseCategory[1].category,
      sub_category: purchaseExpenseCategory[0].subCategory,
      subject:
        invoiceType === "purchase" ? "Purchase Invoice" : "Expense Invoice",
      business: globSelectedBusiness?.business_id,
      assign_to: userInfo?.userId,
    };

    const results = await Promise.allSettled(
      files.map(async (file) => {
        if (!file?.responseId) {
          return Promise.reject(new Error("Missing response ID"));
        }
        data.attachements = [file.responseId];
        return createTicket(data);
      })
    );

    const failedFiles = [];

    results.forEach((result, index) => {
      const file = files[index];
      if (result.status !== "fulfilled") {
        failedFiles.push(file);
      }
    });

    if (failedFiles.length === 0) {
      toast.success("Invoices created successfully");
      setInvoiceType("");
      setFiles([]);
    } else {
      setFiles(failedFiles);
      toast.error(
        `${failedFiles.length} invoice${
          failedFiles.length > 1 ? "s" : ""
        } failed to submit. Please retry.`
      );
    }
  }, [files, invoiceType, globSelectedBusiness, userInfo?.userId]);

  return (
    <LayoutWrapper>
      <div className="min-h-screen p-2">
        {/* Header */}
        <div className="mb-2">
          <h1 className="text-2xl font-bold text-primary-color m-0">
            Create New Invoices
          </h1>
          <p className="text-base text-gray-600 ml-1">
            Upload your invoice documents and select the appropriate type for
            processing
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Section */}
          <div className="space-y-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-primary-color mb-1">
                  Upload Documents
                </h3>
                <p className="text-sm text-gray-500">
                  Only PDF and image files (PNG, JPG, JPEG) are allowed
                </p>
              </div>

              <div
                className={`relative border-2 border-dashed rounded-xl p-8 transition-all duration-200 cursor-pointer ${
                  isDragActive
                    ? "border-accent1 bg-accent1-bg scale-[1.02]"
                    : "border-gray-300 hover:border-accent1 hover:bg-gray-50"
                }`}
                {...eventHandlers}
              >
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="mb-4 relative">
                    <div className="bg-accent1-bg p-4 rounded-full">
                      <FileText className="h-8 w-8 text-accent2" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 bg-accent2 rounded-full p-1.5">
                      <Plus className="h-3 w-3 text-white" />
                    </div>
                  </div>

                  <h4 className="text-lg font-medium text-gray-800 mb-2">
                    Drop files here or click to upload
                  </h4>
                  <p className="text-gray-500 mb-6">
                    Drag and drop your invoice files here
                  </p>

                  <label className="inline-flex items-center px-6 py-3 bg-accent2 hover:bg-accent2-hover text-white font-medium rounded-lg cursor-pointer transition-colors duration-200 shadow-sm">
                    <Upload className="h-5 w-5 mr-2" />
                    Choose Files
                    <input
                      type="file"
                      className="hidden"
                      multiple
                      accept=".pdf, .png, .jpg, .jpeg, application/pdf, image/png, image/jpeg"
                      onChange={(e) => handleFiles(Array.from(e.target.files))}
                    />
                  </label>
                </div>
              </div>
            </div>

            {/* Invoice Type Selection Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <InvoiceTypeSelector
                invoiceType={invoiceType}
                setInvoiceType={setInvoiceType}
              />
            </div>
          </div>

          {/* Files Preview Section - 3 Item Flexbox */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-[calc(100vh-12rem)] flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 pb-4 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-primary-color">
                Uploaded Files
              </h3>
              {files.length > 0 && (
                <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  {files.length} file{files.length !== 1 ? "s" : ""}
                </span>
              )}
            </div>

            {/* Files List */}
            <div className="flex-1 overflow-y-auto p-6 pt-4">
              {files.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <div className="bg-gray-100 p-4 rounded-full mb-4">
                    <FileText className="h-8 w-8 text-gray-400" />
                  </div>
                  <p className="text-gray-500 text-lg font-medium mb-2">
                    No files uploaded yet
                  </p>
                  <p className="text-gray-400 text-sm">
                    Upload files to see them here
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {files.map((file) => (
                    <FilePreview2
                      key={file.id}
                      file={file}
                      onRemove={() =>
                        setFiles((prev) => prev.filter((f) => f.id !== file.id))
                      }
                      onRetry={() => handleRetryUpload(file.id)}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Submit Button*/}
            {isAllFilesUploaded && files.length > 0 && (
              <div className="p-6 pt-4 border-t border-gray-100 bg-gray-50 rounded-b-xl">
                <button
                  onClick={handleSubmit}
                  disabled={invoiceType === ""}
                  className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                    invoiceType === ""
                      ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                      : "bg-accent2 hover:bg-accent2-hover text-white shadow-sm hover:shadow-md"
                  }`}
                  title={
                    invoiceType === "" ? "Please select an invoice type" : ""
                  }
                >
                  {invoiceType === ""
                    ? "Select Invoice Type to Continue"
                    : "Submit Invoices"}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </LayoutWrapper>
  );
}

function InvoiceTypeSelector({ invoiceType, setInvoiceType }) {
  return (
    <>
      <h3 className="text-lg font-semibold text-primary-color mb-2 select-none">
        Invoice Type
        <span className="text-error text-sm ml-1">*</span>
      </h3>

      <div className="grid grid-cols-2 gap-3 select-none">
        <label
          className={`relative flex items-center justify-center p-2 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
            invoiceType === "purchase"
              ? "border-accent2 bg-accent1-bg text-accent2"
              : "border-gray-200 hover:border-gray-300 text-gray-700"
          }`}
        >
          <input
            type="radio"
            name="invoiceType"
            value="purchase"
            checked={invoiceType === "purchase"}
            onChange={() => setInvoiceType("purchase")}
            className="sr-only"
          />
          <div className="text-center">
            <div className="font-medium">Purchase</div>
            <div className="text-xs text-gray-500 mt-1">Purchase invoices</div>
          </div>
          {invoiceType === "purchase" && (
            <div className="absolute top-2 right-2">
              <CheckCircle className="h-4 w-4 text-accent2" />
            </div>
          )}
        </label>

        <label
          className={`relative flex items-center justify-center p-2 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
            invoiceType === "expense"
              ? "border-accent2 bg-accent1-bg text-accent2"
              : "border-gray-200 hover:border-gray-300 text-gray-700"
          }`}
        >
          <input
            type="radio"
            name="invoiceType"
            value="expense"
            checked={invoiceType === "expense"}
            onChange={() => setInvoiceType("expense")}
            className="sr-only"
          />
          <div className="text-center">
            <div className="font-medium">Expense</div>
            <div className="text-xs text-gray-500 mt-1">Expense invoices</div>
          </div>
          {invoiceType === "expense" && (
            <div className="absolute top-2 right-2">
              <CheckCircle className="h-4 w-4 text-accent2" />
            </div>
          )}
        </label>
      </div>
    </>
  );
}

export default InvoicesCreation;
