import React, { Suspense } from "react";
import "./assets/scss/main.scss";
import {
  BrowserRouter as Router,
  Route,
  Routes,
  useLocation,
  useParams,
} from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./routes/ProtectedRoute";
import { mediaBreakpoint } from "./components/global/MediaBreakPointes";
import { Loader } from "semantic-ui-react";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Tooltip } from "react-tooltip";
import ReportsMenu from "./components/screens/ReportsMenu";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const HomeScreen = React.lazy(() =>
  import("./components/screens/home/<USER>")
);
const Login = React.lazy(() => import("./components/Login"));
const ForgetPassword = React.lazy(() => import("./components/ForgotPassword"));
const Error401 = React.lazy(() => import("./components/error-pages/Error401"));
const Error404 = React.lazy(() => import("./components/error-pages/Error404"));
const Error500 = React.lazy(() => import("./components/error-pages/Error500"));
const TicketCreation = React.lazy(() =>
  import("./components/screens/TicketCreation")
);
const TicketView = React.lazy(() =>
  import("./components/screens/ticket-view/TicketView")
);
const Footer = React.lazy(() => import("./components/global/Footer"));
const Notification = React.lazy(() =>
  import("./components/screens/Notification")
);
const ProfileScreen = React.lazy(() =>
  import("./components/screens/profile/ProfileScreen")
);
const BusinessList = React.lazy(() =>
  import("./components/screens/BusinessList")
);
const UserListScreen = React.lazy(() =>
  import("./components/screens/UserListScreen")
);
const BusinessCreation = React.lazy(() =>
  import("./components/screens/BusinessCreation")
);
const BusinessDetailed = React.lazy(() =>
  import("./components/screens/BusinessDetailed")
);
const OrganisationUserList = React.lazy(() =>
  import("./components/screens/OrganisationUsersList")
);
const ArthTattvaUserManagement = React.lazy(() =>
  import("./components/screens/ArthTattvaUserManagement")
);
const TicketManagement = React.lazy(() =>
  import("./components/screens/TicketManagement")
);
const TicketDetails = React.lazy(() =>
  import("./components/screens/TicketDetails")
);
const SystemActivityPage = React.lazy(() =>
  import("./components/screens/SystemActivityPage")
);
const EmailServerConfPage = React.lazy(() =>
  import("./components/screens/EmailServerConf")
);
const FileManagementAdmin = React.lazy(() =>
  import("./components/screens/FileManagementConf")
);
const ReportMainLayout = React.lazy(() =>
  import("./components/screens/phase2/ReportMainLayout")
);
const AiExtractionScreen = React.lazy(() =>
  import("./components/screens/ai-extraction/AiExtractionScreen")
);
const InvoicesScreen = React.lazy(() =>
  import("./components/screens/invoices/InvoicesScreen")
);

const InvoicesCreation = React.lazy(() =>
  import("./components/screens/invoices-creation/InvoicesCreationScreen")
);
// Added configuration component imports

const SyncSettings = React.lazy(() =>
  import("./components/screens/configurations/sync-settings/SyncSettings")
);

const LegdgergroupsScreen = React.lazy(() =>
  import("./components/screens/configurations/LedgergroupsScreen")
);
const LedgerScreen = React.lazy(() =>
  import("./components/screens/configurations/LedgerScreen")
);
const LedgdertypemappingScreen = React.lazy(() =>
  import("./components/screens/configurations/LedgdertypemappingScreen")
);
const Stockitem = React.lazy(() =>
  import("./components/screens/configurations/Stockitem")
);
const StockitemMapping = React.lazy(() =>
  import("./components/screens/configurations/StockitemMapping")
);
const Nonstockitemledger = React.lazy(() =>
  import("./components/screens/configurations/Nonstockitemledger")
);
const Taxledger = React.lazy(() =>
  import("./components/screens/configurations/Taxledger")
);
const Costcenter = React.lazy(() =>
  import("./components/screens/configurations/Costcenter")
);

const LoadingBar = () => (
  <div
    style={{
      height: "100vh",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
    }}
  >
    <Loader active inline="centered" size="large" />
  </div>
);

const queryClient = new QueryClient();

const App = () => {
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <AuthProvider>
          <RoutesWrapper isResponsive={isResponsive} />
        </AuthProvider>
      </Router>
    </QueryClientProvider>
  );
};

const RoutesWrapper = ({ isResponsive }) => {
  const location = useLocation();
  const currentPath = location.pathname;
  const matched = currentPath !== "/login" && currentPath !== "/forgetPassword";
  const params = useParams();

  return (
    <>
      <Suspense fallback={<LoadingBar />}>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/forgetPassword/:reset?" element={<ForgetPassword />} />
          <Route path="/unauthorized" element={<Error401 />} />
          <Route path="/500" element={<Error500 />} />
          <Route path="*" element={<Error404 />} />

          <Route
            path="/"
            element={
              <ProtectedRoute>
                <HomeScreen />
              </ProtectedRoute>
            }
          />
          <Route
            path="/createTicket/:id?"
            element={
              <ProtectedRoute>
                <TicketCreation />
              </ProtectedRoute>
            }
          />
          <Route
            path="/ticket-view/:id"
            element={
              <ProtectedRoute>
                <TicketView />
              </ProtectedRoute>
            }
          />
          <Route
            path="/notification"
            element={
              <ProtectedRoute>
                <Notification />
              </ProtectedRoute>
            }
          />
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <ProfileScreen />
              </ProtectedRoute>
            }
          />
          <Route
            path="/businessList"
            element={
              <ProtectedRoute>
                <BusinessList />
              </ProtectedRoute>
            }
          />
          <Route
            path="/usersList"
            element={
              <ProtectedRoute>
                <UserListScreen />
              </ProtectedRoute>
            }
          />
          <Route
            path="/businessCreation/"
            element={
              <ProtectedRoute requiredRole="superuser">
                <BusinessCreation />
              </ProtectedRoute>
            }
          />
          <Route
            path="/businessDetail/:BuisnessId?"
            element={
              <ProtectedRoute requiredRole="superuser">
                <BusinessDetailed />
              </ProtectedRoute>
            }
          />
          <Route
            path="/ticketDetails/:ticketId?"
            element={
              <ProtectedRoute requiredRole="superuser">
                <TicketDetails />
              </ProtectedRoute>
            }
          />
          <Route
            path="/SystemActivityPage"
            element={
              <ProtectedRoute requiredRole="superuser">
                <SystemActivityPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/EmailServerPage"
            element={
              <ProtectedRoute requiredRole="superuser">
                <EmailServerConfPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/FileManagement"
            element={
              <ProtectedRoute requiredRole="superuser">
                <FileManagementAdmin />
              </ProtectedRoute>
            }
          />
          <Route
            path="/organisationUsers"
            element={
              <ProtectedRoute requiredRole="superuser">
                <OrganisationUserList />
              </ProtectedRoute>
            }
          />
          <Route
            path="/arthtatavaUsers"
            element={
              <ProtectedRoute requiredRole="superuser">
                <ArthTattvaUserManagement />
              </ProtectedRoute>
            }
          />
          <Route
            path="/ticketManagement"
            element={
              <ProtectedRoute requiredRole="superuser">
                <TicketManagement />
              </ProtectedRoute>
            }
          />

          <Route
            path="/reportsMenu"
            element={
              <ProtectedRoute requiredRole="business_superuser">
                <ReportsMenu />
              </ProtectedRoute>
            }
          />

          <Route
            path="/revenue-report"
            element={
              <ProtectedRoute requiredRole="business_superuser">
                <ReportMainLayout />
              </ProtectedRoute>
            }
          />

          <Route
            path="/ledger-report"
            element={
              <ProtectedRoute requiredRole="business_superuser">
                <ReportMainLayout />
              </ProtectedRoute>
            }
          />

          <Route
            path="/expense-report"
            element={
              <ProtectedRoute requiredRole="business_superuser">
                <ReportMainLayout />
              </ProtectedRoute>
            }
          />

          <Route
            path="/receivable-report"
            element={
              <ProtectedRoute requiredRole="business_superuser">
                <ReportMainLayout />
              </ProtectedRoute>
            }
          />
          <Route
            path="/accounts-payables"
            element={
              <ProtectedRoute requiredRole="business_superuser">
                <ReportMainLayout />
              </ProtectedRoute>
            }
          />

          <Route
            path="/inventory-report"
            element={
              <ProtectedRoute requiredRole="business_superuser">
                <ReportMainLayout />
              </ProtectedRoute>
            }
          />

          <Route
            path="/cash-flow"
            element={
              <ProtectedRoute requiredRole="business_superuser">
                <ReportMainLayout />
              </ProtectedRoute>
            }
          />

          <Route
            path="/invoices"
            element={
              <ProtectedRoute requiredRole="accountant">
                <InvoicesScreen />
              </ProtectedRoute>
            }
          />

          <Route
            path="/create-invoice"
            element={
              <ProtectedRoute requiredRole="accountant">
                <InvoicesCreation />
              </ProtectedRoute>
            }
          />

          {/* Added configuration routes */}
          <Route
            path="/sync-settings"
            element={
              <ProtectedRoute requiredRole="accountant">
                <SyncSettings />
              </ProtectedRoute>
            }
          />

          <Route
            path="/ledger-groups"
            element={
              <ProtectedRoute requiredRole="accountant">
                <LegdgergroupsScreen />
              </ProtectedRoute>
            }
          />

          <Route
            path="/ledger"
            element={
              <ProtectedRoute requiredRole="accountant">
                <LedgerScreen />
              </ProtectedRoute>
            }
          />
          <Route
            path="/ledger-type-mapping"
            element={
              <ProtectedRoute requiredRole="accountant">
                <LedgdertypemappingScreen />
              </ProtectedRoute>
            }
          />
          <Route
            path="/stock-item"
            element={
              <ProtectedRoute requiredRole="accountant">
                <Stockitem />
              </ProtectedRoute>
            }
          />
          <Route
            path="/stock-item-mapping"
            element={
              <ProtectedRoute requiredRole="accountant">
                <StockitemMapping />
              </ProtectedRoute>
            }
          />
          <Route
            path="/non-stock-item-ledger"
            element={
              <ProtectedRoute requiredRole="accountant">
                <Nonstockitemledger />
              </ProtectedRoute>
            }
          />
          <Route
            path="/tax-ledger"
            element={
              <ProtectedRoute requiredRole="accountant">
                <Taxledger />
              </ProtectedRoute>
            }
          />
          <Route
            path="/cost-center"
            element={
              <ProtectedRoute requiredRole="accountant">
                <Costcenter />
              </ProtectedRoute>
            }
          />

          <Route
            path="/ai-extraction/f/:fileId/b/:businessId/p/:invoicePage"
            element={
              <ProtectedRoute requiredRole="accountant">
                <AiExtractionScreen />
              </ProtectedRoute>
            }
          />
        </Routes>
      </Suspense>
      {isResponsive && matched && (
        <Suspense fallback={<LoadingBar />}>
          <Footer id={params} />
        </Suspense>
      )}
      <ToastContainer
        position="top-right"
        autoClose={2000}
        hideProgressBar={true}
        closeButton={true}
      />
      <Tooltip id="tooltip" />
      <div id="date-picker-portal" />
    </>
  );
};

export default App;
