import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Modal } from "semantic-ui-react";
import style from "./modalDropdown.module.scss";
import { ChevronRight, Plus, RefreshCcw, Search } from "lucide-react";
import { SuccessIcon, WarningIcon } from "../../../../../assets/svgs";
import Option from "../../../../ui-components/fields/Option";
import useLoadMoreWithSearch from "../../../../global/hooks/useLoadMoreWithSearch";
import { Button } from "@mui/material";

// temporary solution as some APIs structure changed (CostCenter, GST ledger, stock items)
const fetchDataFallback = (data) => {
  if (Array.isArray(data)) return data;
  return data?.data || [];
};

function ModalDropdown({
  label,
  onAdd = null,
  onSelect = null,
  onClose = null,
  options = [],
  disabled = false,
  showMatchesFound = false,
  url = null,
  transformOptionsObj = null,
  searchParamName = "search_query",
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedOption, setSelectedOption] = useState(null);
  const updatedValue = useRef(null);
  const [fetchedOptions, setFetchedOptions] = useState([]);
  const [filteredOptions, setFilteredOptions] = useState([]);
  const fetchUrl = isOpen && (url || null);

  const {
    data: apiResults,
    isLoading,
    nextPageUrl,
    setQuery,
    LoadMoreButton,
    totalCount,
  } = useLoadMoreWithSearch(fetchUrl, searchParamName, 600, true);

  useEffect(() => {
    if (isOpen) {
      setQuery(searchQuery);
    }
  }, [searchQuery, isOpen, setQuery]);

  useEffect(() => {
    if (!url) return;
    setFetchedOptions(apiResults || []);
  }, [apiResults, url]);

  useEffect(() => {
    if (options?.length > 0) {
      const filtered = searchQuery
        ? options.filter((option) =>
            option.label.toLowerCase().includes(searchQuery.toLowerCase())
          )
        : [...options];
      setFilteredOptions(filtered);
    }
  }, [searchQuery, options]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
    setSearchQuery("");
    setFetchedOptions([]);
    setSelectedOption(updatedValue.current);
    onClose && onClose();
  }, [onClose]);

  const transformedOptions = useMemo(() => {
    const fetchData = fetchDataFallback(fetchedOptions);
    const transformedFetchedOptions = url
      ? fetchData.map((item) => ({
          key: item?.[transformOptionsObj?.key] || item?.id,
          label:
            item?.[transformOptionsObj?.label] ||
            item?.ledger ||
            item?.ledger_name,
          value:
            item?.[transformOptionsObj?.value] ||
            item?.ledger ||
            item?.ledger_name,
        }))
      : [];

    // If both URL and options are provided, combine them
    if (url && options.length > 0) {
      return [...transformedFetchedOptions, ...filteredOptions];
    }

    // If only URL is provided, return transformed fetched options
    if (url) {
      return transformedFetchedOptions;
    }

    // If only options are provided, return filtered options
    return filteredOptions?.length > 0 ? filteredOptions : [];
  }, [url, fetchedOptions, filteredOptions, options]);

  const { exactMatches, probableMatches } = useMemo(() => {
    if (options?.length < 1) {
      return { exactMatches: [], probableMatches: [] };
    }

    return filteredOptions.reduce(
      (acc, option) => {
        if (option.similarity_score === 1) {
          acc.exactMatches.push(option);
        } else {
          acc.probableMatches.push(option);
        }
        return acc;
      },
      { exactMatches: [], probableMatches: [] }
    );
  }, [filteredOptions, options]);

  return (
    <>
      {/* dropdown button that triggers modal */}
      <div
        className={`flex items-center my-2 ml-1 text-accent2 cursor-pointer select-none ${
          disabled ? "opacity-50 pointer-events-none cursor-not-allowed" : ""
        }`}
        onClick={() => setIsOpen(true)}
      >
        <div className="flex items-center justify-center gap-2 text-nowrap">
          {showMatchesFound && (
            <span className="text-accent2 font-semibold">
              {options?.length} matches found
            </span>
          )}
          <span className="flex items-center text-nowrap border-b border-accent2">
            {label} <ChevronRight className="w-4 h-4 " />
          </span>
        </div>
      </div>

      <Modal dimmer="blurring" size="small" open={isOpen} onClose={handleClose}>
        <div className={style.dropdownContent}>
          <div className="flex justify-between items-center w-full">
            <h3 className="m-0  font-semibold text-xl text-[#101828]">
              Select an option from below
            </h3>
            <div
              className="flex items-center gap-2 px-4 py-2 rounded-full bg-accent1-bg border border-accent1-border text-lg text-primary-color font-semibold cursor-pointer hover:bg-accent1-bg-hover"
              onClick={() => setSearchQuery("")}
            >
              <RefreshCcw className="w-6 h-6" /> Refresh
            </div>
          </div>

          <div className="relative w-full">
            <input
              className={style.inputField}
              type="text"
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <span className="absolute left-4 top-1/2 -translate-y-1/2 flex items-center justify-center">
              <Search className="w-6 h-6" />
            </span>
            {isLoading && (
              <span className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center justify-center">
                <div className="animate-spin h-5 w-5 border-2 border-[#7e6607] rounded-full border-t-transparent"></div>
              </span>
            )}
          </div>

          {/* add new button */}
          <Button
            onClick={() => onAdd?.()}
            disabled={disabled || !onAdd}
            className={style.addBtn}
          >
            <Plus className="w-6 h-6" />
            Add New
          </Button>

          {/* exact matches */}
          {exactMatches.length > 0 && (
            <div className={style.optionsContainer}>
              <div className={style.optionHeader}>
                <SuccessIcon className="w-7 h-7" />
                <span>Exact Matches ({exactMatches.length})</span>
              </div>
              <div className={style.optionList}>
                {exactMatches.map((option) => (
                  <Option
                    key={`${option.key}-exactMatches`}
                    label={option.label}
                    isChecked={selectedOption?.value === option?.value}
                    className={`${style.option} ${
                      selectedOption?.value === option?.value
                        ? style.selected
                        : ""
                    }`}
                    onClick={() => setSelectedOption(option)}
                  />
                ))}
              </div>
            </div>
          )}

          {/* probable matches */}
          {probableMatches.length > 0 && (
            <div className={style.optionsContainer}>
              <div className={style.optionHeader}>
                <WarningIcon className="w-7 h-7" />
                <span>Probable Matches ({probableMatches.length})</span>
              </div>
              <div className={style.optionList}>
                {probableMatches.map((option) => (
                  <Option
                    key={`${option.key}-probableMatches`}
                    label={option.label}
                    isChecked={selectedOption?.value === option?.value}
                    className={`${style.option} ${
                      selectedOption?.value === option?.value
                        ? style.selected
                        : ""
                    }`}
                    onClick={() => setSelectedOption(option)}
                  />
                ))}
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-4">
              Loading options...
            </div>
          ) : transformedOptions.length === 0 ? (
            <div className="flex items-center justify-center py-4">
              No results found
            </div>
          ) : (
            <div className={style.optionsContainer}>
              <div className={style.optionHeader}>
                <span>
                  All Options ({totalCount || transformedOptions.length})
                </span>
              </div>
              <div className={style.optionList}>
                {transformedOptions.map((option) => (
                  <Option
                    key={`${option.key}-allOptions`}
                    label={option.label}
                    isChecked={selectedOption?.value === option?.value}
                    className={`${style.option} ${
                      selectedOption?.value === option?.value
                        ? style.selected
                        : ""
                    }`}
                    onClick={() => setSelectedOption(option)}
                  />
                ))}
                {nextPageUrl && (
                  <div className="w-full flex justify-center mt-2">
                    {LoadMoreButton}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className={style.actionContainer}>
          <Button className={style.cancelButton} onClick={handleClose}>
            Cancel
          </Button>
          <Button
            className={style.updateButton}
            onClick={() => {
              onSelect && onSelect(selectedOption);
              updatedValue.current = selectedOption;
              handleClose();
            }}
            disabled={!selectedOption}
          >
            Select
          </Button>
        </div>
      </Modal>
    </>
  );
}

export default ModalDropdown;
