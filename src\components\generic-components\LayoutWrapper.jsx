import React from "react";
import style from "./layoutWrapper.module.scss";
import Header from "../global/Header";
import NavigationBar from "../screens/NavigationBar";
import { useAuth } from "../../contexts/AuthContext";

function LayoutWrapper({ children, className }) {
  const { isMobileScreen } = useAuth();
  return (
    <div className={style.mainLayoutContainer}>
      <Header />
      <div className={`${style.layoutWrapperContainer} ${className}`}>
        {!isMobileScreen && (
          <div className={style.leftWrapper}>
            <NavigationBar />
          </div>
        )}
        <div className={style.rightWrapper}>{children}</div>
      </div>
    </div>
  );
}
export default LayoutWrapper;
