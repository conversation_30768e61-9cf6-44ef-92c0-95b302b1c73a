import React, { useState } from "react";
import ConnectToZohoBtn from "./component/ConnectToZohoBtn";
import { ChevronDown, Shield } from "lucide-react";

const regions = ["in"];

function Authentication({ setIsConnected, isConnected, zohoConfigs }) {
  const [zohoRegion, setZohoRegion] = useState(zohoConfigs?.region || "in");
  const [organizationId, setOrganizationId] = useState(
    zohoConfigs?.organization_id || ""
  );

  return (
    <div className="mb-8 p-6 rounded-2xl shadow-lg bg-white border border-accent1-border">
      <div className="flex items-center gap-2 mb-4">
        <div className="flex items-center justify-center w-6 h-6">
          <Shield className="w-5 h-5 text-accent1" />
        </div>
        <label className="text-xl font-bold leading-none text-primary-color">
          Authentication
        </label>
      </div>

      <div className="flex flex-wrap items-end gap-64 mb-6">
        <div className="w-[220px]">
          <label className="block text-lg font-semibold mb-3 ml-3 text-primary-color">
            Organization ID
          </label>
          <input
            type="text"
            value={organizationId}
            onChange={(e) => setOrganizationId(e.target.value)}
            className="w-full px-4 py-3 rounded-xl border-2 focus:outline-none hover:shadow-md  ml-3 bg-accent1-bg border-accent1-border text-primary-color"
            placeholder="Enter your Organization ID"
          />
        </div>
        <div className="w-[180px]">
          <label className="block text-lg font-semibold mb-3 text-primary-color">
            Zoho Region
          </label>
          <div className="relative">
            <select
              value={zohoRegion}
              onChange={(e) => {
                setZohoRegion(e.target.value);
                setIsConnected(false);
                setOrganizationId("");
              }}
              className="w-full text-lg font-medium px-3 py-2 pr-8 rounded-xl border-2 focus:outline-none appearance-none hover:shadow-md text-gray-900 bg-accent1-bg border-accent1-border"
            >
              {regions.map((region) => (
                <option key={region} value={region}>
                  .{region}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none text-accent1" />
          </div>
        </div>
      </div>

      <ConnectToZohoBtn
        setIsConnected={setIsConnected}
        isConnected={isConnected}
        organizationId={organizationId}
        region={zohoRegion}
      />
    </div>
  );
}

export default Authentication;
