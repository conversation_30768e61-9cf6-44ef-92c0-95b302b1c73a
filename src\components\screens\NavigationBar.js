
import React, { useState, useEffect, useRef } from "react";
import style from "./scss/navigationBar.module.scss";
// import { useAuth } from "../../contexts/AuthContext";
import { Image, Loader, Accordion } from "semantic-ui-react";
import { useNavigate, useLocation } from "react-router-dom";
import ls from "local-storage";
import { rightArrowIcon } from "../global/Icons";
import { useAuth } from "../../contexts/AuthContext";
import GlobalService from "../services/GlobalServices";
import { resturls } from "../utils/apiurls";
import SyncMailBtn from "../global/components/SyncMailBtn";
import {
  defaultLogo,
  plusIcon,
  homeIcon,
  logIcon,
  reportIcon,
  menuIcon,
  defaultLogo2,
  audioIcon,
  helpIcon,
  dashboardIcon,
  organisationIcon,
  dropdownIcon,
  emailServerIcon,
  ticketManageIcon,
  systemActivityIcon,
  FileManagementIcon,
} from "../global/Icons";
import { decryptData } from "../utils/cryptoUtils";
import { ChevronRight, FileText, Settings } from "lucide-react";
import SlicedText from "../generic-components/SlicedText";

const NavigationBar = () => {
  // const { businessList } = useAuth();
  const { headerLogo, userInfo, setBusiness, globSelectedBusiness, setGlobSelectedBusiness, roleType } = useAuth();
  const [activeMenu, setActiveMenu] = useState("home");
  const [showListContainer, handleListContainer] = useState(false);
  const [businessList, setBusinessList] = useState([]);
  const [selectedBusiness, setSelectedBusiness] = useState(globSelectedBusiness?.business_id || null);
  const [adminCount, setAdminCount] = useState(0);
  const [isLoading, setIsloading] = useState(false);
  const [activeIndex, setActiveIndex] = useState(null);
  const [activeSubMenu, setActiveSubMenu] = useState(0);

  const navigate = useNavigate();
  const location = useLocation();
  const roleEncripted = ls.get("access_token")?.role;
  const role = roleEncripted && decryptData(roleEncripted);
  const matched = ["business_user", "business_superuser", "accountant", "superuser"].includes(role);
  const isAdmin = role === "superuser";
  const accordContentRef = useRef(null);
  const activeSubMenuRef = useRef(null);

  useEffect(() => {
    if (activeSubMenuRef.current && accordContentRef.current) {
      const parent = accordContentRef.current;
      const child = activeSubMenuRef.current;

      const parentHeight = parent.clientHeight; // Visible height (250px)
      const parentScrollTop = parent.scrollTop; // Current scroll position
      const childOffsetTop = child.offsetTop; // Position of active item inside parent
      const childHeight = child.clientHeight; // Height of active item

      // Get the middle position of the parent container
      const parentMiddle = parentHeight / 2;

      // Get the current position of the active item within the parent
      const childPositionInParent = childOffsetTop - parentScrollTop;

      // Determine if we need to scroll
      let scrollTo = null;
      if (childPositionInParent > parentMiddle + childHeight) {
        // If item is **below** the 60% range, scroll down
        scrollTo = childOffsetTop - parentMiddle + childHeight / 2;
      } else if (childPositionInParent < parentMiddle - childHeight) {
        // If item is **above** the 40% range, scroll up
        scrollTo = childOffsetTop - parentMiddle + childHeight / 2;
      }
      if (scrollTo !== null) {
        parent.scrollTo({
          top: scrollTo,
          behavior: "smooth",
        });
      }
    }
  }, [activeSubMenu]);

  useEffect(() => {
    GlobalService.generalSelect(
      (respdata) => {
        const { data } = respdata;
        setAdminCount(data?.length || 0);
      },
      `${resturls.obtainCategoryWiseUser}?user_type=superusers`,
      {},
      "GET"
    );
  }, []);

  const reportPaths = [
    "/revenue-report",
    "/ledger-report",
    "/expense-report",
    "/receivable-report",
    "/accounts-payables",
    "/inventory-report",
    "/cash-flow",
  ];

  const handleChange = (value) => {
    setSelectedBusiness(value);
    const headerlogo = value.business_image || defaultLogo;
    ls.set("selectedBusiness", value);
    ls.set("globSelectedBusiness", value);
    setGlobSelectedBusiness(value);
    handleListContainer(false);
    const currentPath = location.pathname;

    const features = value?.features || []; // Use `value.features` instead of `selectedBusiness.features`
    const isReportsEnabled = features.some(
      (feature) => feature.name === "Reports" && feature.is_active
    );

    if (reportPaths.includes(currentPath) && !isReportsEnabled) {
      navigate("/");
    } else {
      const keysToRemove = [
        "revenueDetails", "revenueTrendLineDetails", "revenueDetailedReport",
        "CostReportPayload", "CostDetailsTrendlineData", "details",
        "inventoryDetails", "payableData", "customerData", "invoiceData",
        "projectCashFLow", "receivableDetails", "receivableData",
        "Cost Report Page_logs", "ledgerTimeline", "CostDetailedReport",
        "Revenue Report Page_logs", "activeAccountList", "ledgerReportList",
        "overviewInfo", "transactionInfo", "cashFlowData",
        "totalIncome", "totalExpense"
      ];

      keysToRemove.forEach(key => ls.remove(key));

      if (window.location.pathname === "/") {
        window.location.reload();
      }
    }

    if (currentPath.includes("ticketList")) {
      navigate(`/ticketList/${roleType === "user" ? value.business_id : ""}`);
    }
  };

  // Added the configuration submenu list definition
  const configSubmenuList = [
    { name: "Sync Settings", key: "sync-settings", url: "/sync-settings" },
    { name: "Ledger Groups", key: "ledger-groups", url: "/ledger-groups" },
    { name: "Ledger", key: "ledger", url: "/ledger" },
    { name: "Ledger Type Mapping", key: "ledger-type-mapping", url: "/ledger-type-mapping" },
    { name: "Stock Item", key: "stock-item", url: "/stock-item" },
    { name: "Stock Item Mapping", key: "stock-item-mapping", url: "/stock-item-mapping" },
    { name: "Non Stock Item Ledger", key: "non-stock-item-ledger", url: "/non-stock-item-ledger" },
    { name: "Tax Ledger", key: "tax-ledger", url: "/tax-ledger" },
    { name: "Cost Center", key: "cost-center", url: "/cost-center" },
  ];

  const [menuList, setMenuList] = useState([
    {
      name: "Home",
      url: "/",
      key: "home",
      icon: homeIcon(),
    },
    {
      name: "Invoices",
      key: "invoices",
      icon: <FileText strokeWidth={1.5} className="stroke-[#535862]" />,
      url: "/invoices",
    },
    {
      name: "Reports",
      key: "reports",
      icon: reportIcon(),
    },
    {
      name: "Configuration ",
      key: "configurationList",
      icon: <Settings strokeWidth={1.5} className="stroke-[#535862]" />,
      submenu: configSubmenuList,
    }
  ]);

  useEffect(() => {
    const newList = [
      {
        name: "Dashboard",
        key: "dashboard",
        icon: dashboardIcon(),
        url: `/`,
      },
      {
        name: "Organisations",
        key: "organisations",
        icon: organisationIcon(),
        url: `/businessList`,
      },
      {
        name: "Ticket Management",
        key: "tiketManagement",
        icon: ticketManageIcon(),
        url: `/ticketManagement`,
      },
      {
        name: "System Activity",
        key: "systemActivity",
        icon: systemActivityIcon(),
        url: `/SystemActivityPage`,
        strokeType: true,
      },
      {
        name: "Email Server",
        key: "EmailServer",
        icon: emailServerIcon(),
        url: `/EmailServerPage`,
      },
      {
        name: "File Management",
        key: "FileManagement",
        icon: FileManagementIcon(),
        url: `/FileManagement`,
        strokeType: true,
      },
    ];
    if (["superuser"].includes(role)) {
      setMenuList(newList);
    }
  }, [role]);

  useEffect(() => {
    const currentPath = location.pathname;
    if (currentPath === "/") {
      setActiveMenu(["superuser"].includes(role) ? "dashboard" : "home");
      setActiveSubMenu(null);
    } else if (currentPath.includes("/createTicket")) {
      setActiveMenu(null);
      setActiveSubMenu(null);
    } else {
      let activeItem = menuList.find(
        (item) =>
          item.url && currentPath.
            startsWith(item.url) && item.
              url !== "/"
      );

      if (!activeItem) {
        menuList.forEach((menu, i) => {
          if (menu.submenu) {
            const subItem = menu.submenu.find((sub) =>
              currentPath === sub.url
            );
            if (subItem) {
              activeItem = { key: menu.key };
              setActiveSubMenu(subItem.key);
              setActiveIndex(i);
            }
          }
        });
      }

      if (activeItem) {
        setActiveMenu(activeItem.key);
      } else {
        setActiveMenu(null);
      }
    }
  }, [location.pathname, menuList]);

  const handleActiveMenu = (item, index) => {
    if (item.submenu) {
      setActiveIndex(activeIndex === index ? null : index);

    } else {
      setActiveIndex(index);
    }
    if (item?.url && !isLoading) {
      if (item.key === "ticketList") {
        navigate(
          `/ticketList/${roleType === "user"
            ? selectedBusiness?.business_id || userInfo?.business_id
            : ""
          }`
        );
      } else {
        navigate(item.url);
      }
    }
  };

  useEffect(() => {
    const userId = userInfo?.user_id || userInfo?.userId;
    if (userId) {
      setIsloading(true);
      const delayCall = setTimeout(() => {
        GlobalService.generalSelect(
          (respdata) => {
            const { results } = respdata;
            setBusinessList(results);
            setBusiness(results);
            const selectedInLs = ls.get("selectedBusiness");
            setIsloading(false);
            if (results?.length > 0) {
              const matchingBusiness = results.find(
                (business) => business?.business_image ===
                  headerLogo
              );

              if (selectedInLs) {
                setSelectedBusiness(selectedInLs);
              } else {
                if (matchingBusiness) {
                  setSelectedBusiness(matchingBusiness);
                } else {
                  setSelectedBusiness(results[0]);
                  ls.set("selectedBusiness", results[0]);
                }
              }
            }
          },
          `${resturls.getBusinesses}`,
          {},
          "GET"
        );
      }, 100);
      return () => clearTimeout(delayCall);
    }
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      const features = selectedBusiness?.features || [];
      const isReportsEnabled = features.some(
        (feature) => feature.name === "Reports" && feature.is_active
      );

      if (reportPaths.includes(location.pathname) && !isReportsEnabled) {
        navigate("/");
      }
    }, 1500);

    return () => clearTimeout(timer); // Cleanup on unmount or dependency change
  }, [location.pathname, selectedBusiness]);

  useEffect(() => {
    if (!selectedBusiness) return;

    const features = selectedBusiness?.features || [];
    const isReportsEnabled = features.some(
      (feature) => feature.name === "Reports" && feature.is_active
    );

    const submenuList = [
      { name: "Revenue Report", key: "revenue-report", url: "/revenue-report" },
      { name: "Expense Report", key: "expense-report", url: "/expense-report" },
      { name: "Accounts/Ledger Report", key: "accounts-ledger-report", url: "/ledger-report" },
      { name: "Inventory Report", key: "inventory-report", url: "/inventory-report" },
      { name: "Total Receivable", key: "receivable-report", url: "/receivable-report" },
      { name: "Total Payable", key: "accounts-payables", url: "/accounts-payables" },
      { name: "Project Cash Flow", key: "project-cash-flow", url: "/cash-flow" },
    ];

    const businessUserMenuList = [
      {
        name: "Home",
        url: "/",
        key: "home",
        icon: homeIcon(),
      },
      {
        name: "Invoices",
        key: "invoices",
        icon: <FileText strokeWidth={1.5} className="stroke-[#535862]" />,
        url: "/invoices",
      },
      {
        name: "Reports",
        key: "reports",
        icon: reportIcon(),
        submenu: isReportsEnabled ? submenuList : null,
      },
      {
        name: "Configuration ",
        key: "configurationList",
        icon: <Settings strokeWidth={1.5} className="stroke-[#535862]" />,
        submenu: configSubmenuList,
      }
    ];

    if (["business_superuser"].includes(role)) {
      setMenuList
        (businessUserMenuList);
    }
  }, [selectedBusiness, role]);

  const bussinessOptions = businessList?.map((business, index) => ({
    key: index,
    text: business.business_name,
    value: business,
    logo: business?.business_image,
  }));

  if (isAdmin) {
    return (
      <>
        <div className={`${style.adminContainer} ${style.menuListContainer}`}>
          {
            <div className={style.selectedBusiness}>
              <div>
                <p className={style.orgName}>ArthTattva</p>
                <p className={style.desc}>{adminCount} Admin Members</p>
              </div>
              {/* <div onClick={() => handleListContainer(true)} >{whitePlusIcon()}</div> */}
            </div>
          }
          <div className={style.menuList}>
            <ol>
              {menuList.map((item, index) => (
                <li
                  key={item.key}
                  className={`${item?.className || ""} ${activeMenu === item.key && item?.strokeType
                    ? style.strokeType
                    : ""
                    } ${activeMenu === item.key ? style.activeMenu : ""} `}
                  onClick={() => handleActiveMenu(item, index)}
                >
                  {item?.name && item?.icon}
                  {item?.name || item.icon}
                </li>
              ))}
            </ol>
          </div>
        </div>
      </>
    );
  }

  const handleSubMenuActive = (info, index) => {
    setActiveSubMenu(info?.key); // Added missing line from first code
    navigate(info?.url);
  };

  return (
    <>
      <div className={style.mainWrapper}>
        <div className={style.menuListContainer}>
          {matched && (
            <div
              className={style.selectedBusiness}
              onClick={() => handleListContainer(!showListContainer)}
            >
              <div className={style.logoWrapper}>
                {selectedBusiness?.business_image ? (
                  <Image src={selectedBusiness?.business_image} />
                ) : (
                  defaultLogo()
                )}
              </div>
              <p className={style.selectedBusinessName}>
                {isLoading ? (
                  <Loader active size="small" />
                ) : (
                  <SlicedText text={selectedBusiness?.business_name} sliceTill={40} />
                )}
              </p>
              <div>
                <ChevronRight className="w-10 h-10" />
              </div>
            </div>
          )}

          <div className="flex flex-col gap-2">
            <div
              className={style.recordBtn}
              onClick={() =>
                navigate("/create-invoice")
              }
            >
              {plusIcon()} New Invoice
            </div>
            <SyncMailBtn />
          </div>

          {role === "superuser" && (
            <div
              className={style.recordBtn}
              onClick={() =>
                role === "superuser" && navigate("/businessCreation")
              }
            >
              {plusIcon()} Create Business
            </div>
          )}
          <div className={style.menuList}>
            <ol>
              {menuList.map((item, index) => (
                <li
                  key={item.key}
                  className={`${activeMenu === item.key && !item?.submenu
                    ? style.activeMenu
                    : ""
                    } ${item?.submenu ? style.subMenuHeader : ""}`}
                  onClick={() => !item?.submenu && handleActiveMenu(item, index)}
                >
                  {item?.submenu ? (
                    <Accordion className={`${style.accordion}  pb-16`}>
                      <Accordion.Title className={style.accordTitle}>
                        <div
                          className={style.accordMenu}
                          onClick={() => handleActiveMenu(item, index)}
                        >
                          <div>
                            <span className={style.reportIcon}>
                              {item.icon}
                            </span>
                            {item?.name}
                          </div>
                          <span
                            className={`${style.dropdownIcon} ${activeIndex === index ? style.activeDropdown : ""
                              }`}
                          >
                            {dropdownIcon()}
                          </span>
                        </div>
                      </Accordion.Title>
                      <Accordion.Content active={activeIndex === index}  >
                        <div ref={accordContentRef} className={style.accordContent}>
                          {item.submenu && item.submenu.map((subItem, subIndex) => (
                            <li
                              key={subItem.key}
                              className={`subMenuItem ${activeSubMenu === subItem?.key ? style.activeMenu : ''}`}
                              onClick={() => handleSubMenuActive(subItem, subIndex)}
                              ref={activeSubMenu === subItem?.key ? activeSubMenuRef : null}
                            >
                              {subItem.name}
                            </li>
                          ))}
                        </div>
                      </Accordion.Content>
                    </Accordion>
                  ) : (
                    // Regular menu item
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "1em",
                      }}
                      className={`menuItem`}
                    >
                      {item.icon}
                      {item.name}
                    </div>
                  )}
                </li>
              ))}
            </ol>
          </div>
        </div>
        {
          <div
            className={`${showListContainer ? style.activeContent : ""} ${style.businessListContainer
              }`}
          >
            <div
              className={`${style.bussinessListConatiner} ${showListContainer ? style.activeListContainer : ""
                }`}
            >
              <p>Your Organisations{` (${bussinessOptions?.length})`}</p>
              {bussinessOptions?.map((item) => {
                return (
                  <div
                    key={item.key} // Added missing key
                    className={`${style.menuItem} ${selectedBusiness?.business_name === item?.text
                      ? style.activeMenu
                      : ""
                      }`}
                    onClickCapture={() => handleChange(item?.value)}
                  >
                    <div className={style.logoWrapper}>
                      {item?.logo ? <Image src={item?.logo} /> : defaultLogo2()}
                    </div>
                    <p>{item?.text}</p>
                  </div>
                );
              })}
            </div>
          </div>
        }
      </div>
    </>
  );
};

export default NavigationBar;
