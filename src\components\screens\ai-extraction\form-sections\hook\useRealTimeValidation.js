import { useCallback, useEffect, useRef, useState } from "react";
import { sectionValidation } from "../../../../services/aiServices";
import { getErrorMessage } from "../../../../utils/apiUtils";
import { toast } from "react-toastify";
import { isEqual } from "lodash";
import { useParams } from "react-router-dom";
import { removeObjectsFromJson } from "../../../../utils/jsonUtils";
import { getSectionsWithErrors } from "../../../../utils/aiUtils";

function useRealTimeValidation({
  data,
  formAction,
  defaultSuggestion = null,
  debounceDelay = 0,
  onValidationSuccess,
  onValidationError,
  isInventoryEnabled = null,
}) {
  const [suggestions, setSuggestions] = useState(defaultSuggestion || {});
  const previousData = useRef(null);
  const { businessId } = useParams();
  const { fileId } = useParams();

  useEffect(() => {
    setSuggestions(defaultSuggestion);
  }, [defaultSuggestion]);

  const handleRealTimeValidation = useCallback(
    (
      section,
      invoiceType,
      currentSectionData,
      doNotUseSectionWrapper = false,
      loadingSetter = null
    ) => {
      if (isEqual(previousData.current, currentSectionData)) return;
      loadingSetter && loadingSetter(true);
      previousData.current = currentSectionData;
      const body = doNotUseSectionWrapper
        ? { ...currentSectionData, is_inventory_enabled: isInventoryEnabled }
        : {
            [section]: currentSectionData,
            is_inventory_enabled: isInventoryEnabled,
          };
      sectionValidation(fileId, businessId, section, invoiceType, body)
        .then((res) => {
          const sectionData = res?.[section];
          formAction("HARD_UPDATE_SECTION", section, null, sectionData);
          const recommendedFields = sectionData?.recommended_fields?.[0] ?? {};
          const isSimilarityScore = "similarity_score" in recommendedFields;
          if (isSimilarityScore) {
            setSuggestions(null);
          } else {
            setSuggestions(sectionData.recommended_fields?.[0] ?? null);
          }
          if (onValidationSuccess) {
            onValidationSuccess(sectionData);
          }
        })
        .catch((err) => {
          const errorMessage = getErrorMessage(
            err,
            `Server error - Can't validate ${section}`
          );
          toast.error(errorMessage);
          if (onValidationError) {
            onValidationError(errorMessage);
          }
        })
        .finally(() => {
          loadingSetter && loadingSetter(false);
        });
    },
    [data]
  );

  const handleValidate = useCallback(
    (
      section,
      invoiceType,
      propData = null,
      doNotUseSectionWrapper = false,
      loadingSetter = null
    ) => {
      const whichToUseData = propData ? propData : data;
      const body = removeObjectsFromJson(whichToUseData, [
        "error",
        "warning",
        "exact_match",
        "recommended_fields",
      ]);
      handleRealTimeValidation(
        section,
        invoiceType,
        body,
        doNotUseSectionWrapper,
        loadingSetter
      );
    },
    [data]
  );

  return [handleValidate, suggestions, setSuggestions];
}

export default useRealTimeValidation;
