import apiClient from "./apiClient";
import { aiUrls, resturls } from "../utils/apiurls";

export const processInvoice = (fileId, businessId) =>
  apiClient.post(`${resturls.extractInvoice}?business_id=${businessId}`, {
    file_id: fileId,
  });

export const getExtractedInvoice = (fileId, businessId) =>
  apiClient.get(
    `${resturls.extractedInvoiceRootDomain}${fileId}?business_id=${businessId}`
  );

export const saveDraft = (fileId, businessId, draftData) =>
  apiClient.post(
    `${resturls.extractedInvoiceRootDomain}${fileId}/draft?business_id=${businessId}`,
    draftData
  );

export const submitExtraction = (fileId, businessId, extractionData) =>
  apiClient.post(
    `${resturls.extractedInvoiceRootDomain}${fileId}/publish?business_id=${businessId}`,
    extractionData
  );

export const sectionValidation = (
  fileId,
  businessId,
  sectionName,
  flowName,
  payload
) =>
  apiClient.post(
    `${resturls.extractedInvoiceRootDomain}${fileId}/validate_section?business_id=${businessId}&section_name=${sectionName}&flow_name=${flowName}`,
    payload
  );

export const invoiceComment = (fileId, businessId, payload) =>
  apiClient.post(
    `${resturls.extractedInvoiceRootDomain}${fileId}/comments?business_id=${businessId}`,
    payload
  );

export const getOriginalInvoice = (fileId, businessId) =>
  apiClient.get(
    `${resturls.extractedInvoiceRootDomain}${fileId}/original_invoice?business_id=${businessId}`
  );

export const addZohoItems = (businessId, payload) =>
  apiClient.post(`${aiUrls.addZohoItems}?business_id=${businessId}`, payload);

export const addZohoSupplier = (businessId, payload) =>
  apiClient.post(
    `${aiUrls.addZohoSupplier}?business_id=${businessId}`,
    payload
  );

export const syncZoho = (businessId, payload) =>
  apiClient.post(`${aiUrls.syncZoho}?business_id=${businessId}`, payload);
