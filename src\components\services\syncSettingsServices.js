import { syncSettingsUrls } from "../utils/apiurls";
import apiClient from "./apiClient";

export const connectToZoho = async (businessId, organizationId, region) => {
  return apiClient.post(
    `${syncSettingsUrls.connectToZoho}?business_id=${businessId}&organization_id=${organizationId}&region=${region}`
  );
};

export const getAccountingPlatforms = () => {
  return apiClient.get(`${syncSettingsUrls.getAccountingPlatforms}/`);
};

export const zohoCallback = (businessId, restAuthParams) => {
  return apiClient.get(
    `${syncSettingsUrls.zohoCallback}?business_id=${businessId}&${restAuthParams}`
  );
};

export const getBusinessPreferences = (businessId) => {
  return apiClient.get(
    `${syncSettingsUrls.businessPreferences}/${businessId}/`
  );
};

export const updateBusinessPreferences = (businessId, payload) => {
  return apiClient.patch(
    `${syncSettingsUrls.businessPreferences}/${businessId}/`,
    payload
  );
};

export const getZohoConfigs = (businessId) => {
  return apiClient.get(
    `${syncSettingsUrls.zohoConfigs}?business_id=${businessId}`
  );
};

export const getSyncStatus = (businessId) => {
  return apiClient.get(
    `${syncSettingsUrls.syncStatus}?business_id=${businessId}`
  );
};

export const fullSync = (businessId) => {
  return apiClient.get(
    `${syncSettingsUrls.fullSync}?business_id=${businessId}`
  );
};
