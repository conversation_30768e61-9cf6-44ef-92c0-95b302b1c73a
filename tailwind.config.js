/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        "primary-color": "#0B1A30",
        "secondary-color": "#F5F7FA",
        "accent1": "#4AC2FF",
        "accent1-bg": "#4AC2FF1A",
        "accent1-border": "#4AC2FF50",
        "accent1-bg-hover": "#4AC2FF33",
        "accent2": "#2186D0",
        "accent2-hover": "#1B6CAF",
        "error": "#FF6B6B",
        "error-bg": "#FF6B6B1A",
        "error-bg-hover": "#FF6B6B33",
        "error-border": "#FF6B6B50",
        "success": "#5CD68A",
        "success-bg": "#5CD68A1A",
        "success-bg-hover": "#5CD68A33",
        "success-border": "#5CD68A50",
        "gray-bg": "#F3F4F6",
      },
      keyframes: {
        fadePulse: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.5' }
        }
      }
    },
  },
  plugins: [],
};
