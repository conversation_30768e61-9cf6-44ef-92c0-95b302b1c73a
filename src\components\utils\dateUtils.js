import { parse, isValid } from "date-fns";

/**
 * Formats a date range into a readable string.
 * @param {string} start_time - The start time.
 * @param {string} end_time - The end time.
 * @param {boolean} singleDate - Whether to format as a single date.
 * @returns {string} - The formatted date range.
 */

export const formatDateRange = (start_time, end_time, singleDate) => {
  const today = new Date();
  const startDate = new Date(start_time);
  const endDate = new Date(end_time);

  if (isNaN(startDate.getTime()) && singleDate) {
    return start_time || "-"; // Return "-" for invalid dates
  }

  // Check if the start date is in "YYYY-Www" format (e.g., "2024-W01")
  const isWeekFormat = start_time?.match(/^\d{4}-W\d{2}$/);

  // If it's a week format and singleDate is true, return the week string
  if (singleDate && isWeekFormat) {
    return start_time; // Return the week in the "YYYY-Www" format
  }

  // Otherwise, proceed with the regular date formatting
  const startDay = startDate.getUTCDate();
  const startMonth = startDate.toLocaleString("en-US", { month: "short" });
  const startYear = startDate.getUTCFullYear();
  if (singleDate) {
    const diffInTime = today.getTime() - startDate.getTime();
    const diffInDays = Math.floor(diffInTime / (1000 * 60 * 60 * 24));
    const isToday = diffInDays === 0;
    return `${startDay} ${startMonth} ${startYear} ${isToday ? "(Today)" : ""}`;
  }
  const endDay = endDate.getUTCDate();
  const endMonth = endDate.toLocaleString("en-US", { month: "short" });
  const endYear = endDate.getUTCFullYear();

  // If the months are different, include both months in the output
  if (startYear !== endYear) {
    return `${startDay} ${startMonth} ${startYear} – ${endDay} ${endMonth} ${endYear}`;
  } else {
    return `${startDay} ${startMonth} – ${endDay} ${endMonth} ${endYear}`;
  }
};

/**
 * Calculates the number of days between two dates.
 * @param {string} startDate - The start date.
 * @param {string} endDate - The end date.
 * @returns {number} - The number of days between the dates.
 */
export const getDaysBetweenDates = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const differenceInTime = end - start;
  const differenceInDays = Math.ceil(differenceInTime / (1000 * 60 * 60 * 24));
  return differenceInDays;
};

/**
 * Formats a date into YYYY-MM-DD format.
 * @param {Date} date - The date to format.
 * @returns {string} - The formatted date.
 */
export const formatDateYYMMDD = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

/**
 * Gets the date range based on a given value.
 * @param {string} value - The value to determine the date range.
 * @returns {Object} - The start and end dates.
 */
export const getDateRange = (value) => {
  const currentDate = new Date();
  let startDate = new Date();
  let endDate = new Date(); // End date will be today by default

  switch (value) {
    case "last7days":
      startDate.setDate(currentDate.getDate() - 6);
      break;

    case "monthToDate":
      startDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        1
      ); // First day of the month
      endDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() + 1,
        0
      ); // Last day of the month
      break;

    case "last30days":
      startDate.setDate(currentDate.getDate() - 29); // Last 30 days includes today
      break;

    case "quarterToDate":
      const quarter = Math.floor(currentDate.getMonth() / 3);
      const quarterStartMonth = quarter * 3;
      startDate = new Date(currentDate.getFullYear(), quarterStartMonth, 1);
      endDate = new Date(currentDate.getFullYear(), quarterStartMonth + 3, 0); // Last day of the quarter
      break;

    case "yearToDate":
      const financialYearStart =
        currentDate.getMonth() >= 3
          ? currentDate.getFullYear()
          : currentDate.getFullYear() - 1;
      startDate = new Date(financialYearStart, 3, 1); // April 1
      endDate = new Date(financialYearStart + 1, 2, 31); // March 31 of the next year
      break;

    default:
      console.log("Invalid value provided");
      break;
  }

  // Format date as DD/MM/YYYY
  const formatDate = (date) => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const year = date.getFullYear();
    // return `${day}-${month}-${year}`;
    return `${year}-${month}-${day}`;
  };

  return {
    startDate: formatDate(startDate),
    endDate: formatDate(endDate),
  };
};

/**
 * Gets the previous trendline dates based on a selected option.
 * @param {string} selectedOption - The selected option.
 * @returns {Object|null} - The previous start and end dates.
 */
export const getPreviousTrendlineDates = (selectedOption) => {
  const currentDate = new Date();
  const formatDate = (date) => {
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000)
      .toISOString()
      .split("T")[0];
  };

  let previousStartDate, previousEndDate;

  switch (selectedOption) {
    case "last7days":
      previousEndDate = new Date(currentDate);
      previousEndDate.setDate(previousEndDate.getDate() - 7);
      previousStartDate = new Date(previousEndDate);
      previousStartDate.setDate(previousStartDate.getDate() - 7);
      break;

    case "last30days":
      previousEndDate = new Date(currentDate);
      previousEndDate.setDate(previousEndDate.getDate() - 30);
      previousStartDate = new Date(previousEndDate);
      previousStartDate.setDate(previousStartDate.getDate() - 30);
      break;

    case "monthToDate":
      const prevMonth =
        currentDate.getMonth() === 0 ? 11 : currentDate.getMonth() - 1;
      const prevYear =
        currentDate.getMonth() === 0
          ? currentDate.getFullYear() - 1
          : currentDate.getFullYear();
      previousStartDate = new Date(prevYear, prevMonth, 1);
      previousEndDate = new Date(prevYear, prevMonth, currentDate.getDate());
      break;

    case "quarterToDate":
      const currentQuarterStartMonth =
        Math.floor(currentDate.getMonth() / 3) * 3;
      const currentQuarterStartDate = new Date(
        currentDate.getFullYear(),
        currentQuarterStartMonth,
        1
      );

      const daysElapsed = Math.floor(
        (currentDate - currentQuarterStartDate) / (1000 * 60 * 60 * 24)
      );

      let prevQuarterStartMonth = currentQuarterStartMonth - 3;
      let prevQuarterYear = currentDate.getFullYear();

      if (prevQuarterStartMonth < 0) {
        prevQuarterStartMonth += 12;
        prevQuarterYear -= 1;
      }

      previousStartDate = new Date(prevQuarterYear, prevQuarterStartMonth, 1);
      previousEndDate = new Date(previousStartDate);
      previousEndDate.setDate(previousEndDate.getDate() + daysElapsed);
      break;

    case "yearToDate":
      previousStartDate = new Date(currentDate.getFullYear() - 1, 0, 1);
      previousEndDate = new Date(
        currentDate.getFullYear() - 1,
        currentDate.getMonth(),
        currentDate.getDate()
      );
      break;

    default:
      console.log("Invalid dropdown option selected");
      return null;
  }

  return {
    previousStartDate: formatDate(previousStartDate),
    previousEndDate: formatDate(previousEndDate),
  };
};

/**
 * Formats a timestamp into a readable string.
 * @param {string} timestamp - The timestamp to format.
 * @returns {string} - The formatted timestamp.
 */
export const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp);
  return date
    .toLocaleString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    })
    .replace(",", " at")
    .replace(/\bam\b/g, "AM") // Convert "am" to "AM"
    .replace(/\bpm\b/g, "PM"); // Convert "pm" to "PM"
};

/**
 * Reverses a date string from YYYY-MM-DD to DD-MM-YYYY.
 * @param {string} date - The date string to reverse.
 * @returns {string} - The reversed date string.
 */
export const reverseDate = (date) => {
  return date?.split("-").reverse().join("-");
};

/**
 * Formats a date string to YYYY-MM-DD format considering timezone.
 * @param {string} isoString - The ISO date string.
 * @returns {string} - The formatted date string.
 */
export const formatTimezoneDate = (isoString) => {
  const date = new Date(isoString);
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(date.getUTCDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};

/**
 * Formats a given amount into Indian currency format.
 *
 * @param {number|string} amount - The amount to format.
 * @returns {string} - The formatted currency string.
 */
export const formatIndianCurrency = (amount, isReturnNull = false) => {
  const num = Number(amount);
  if (isNaN(num)) return isReturnNull ? null : "0";
  return num.toLocaleString("en-IN");
};

/**
 * Determines the granularity of data based on the date range.
 *
 * @param {string} startDate - The start date in string format.
 * @param {string} endDate - The end date in string format.
 * @returns {string} - Returns "month" if range is 90+ days, "week" if 30+ days, else "day".
 */
export const getGranularity = (startDate, endDate) => {
  let count = getDaysBetweenDates(startDate, endDate);
  let num = Number(count);
  if (num >= 90) {
    return "month";
  } else if (num > 30) {
    return "week";
  } else {
    return "day";
  }
};

/**
 * Calculates the previous date range based on a given timeline.
 *
 * @param {string} startDate - The start date of the current range.
 * @param {string} endDate - The end date of the current range.
 * @param {Object} timeLine - The timeline object containing the value of the period.
 * @returns {Object} - An object with `startDate` and `endDate` of the previous range.
 */
export const getPreviousDateRange = (startDate, endDate, timeLine) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffInDays = Math.floor((end - start) / (1000 * 60 * 60 * 24));

  let prevStartDate, prevEndDate;

  if (timeLine?.value === "quarterToDate") {
    const month = start.getMonth();
    const year = start.getFullYear();
    let prevQuarterStartMonth, prevQuarterYear;

    if (month <= 2) {
      prevQuarterStartMonth = 9;
      prevQuarterYear = year - 1;
    } else if (month <= 5) {
      prevQuarterStartMonth = 0;
      prevQuarterYear = year;
    } else if (month <= 8) {
      prevQuarterStartMonth = 3;
      prevQuarterYear = year;
    } else {
      prevQuarterStartMonth = 6;
      prevQuarterYear = year;
    }

    prevStartDate = new Date(prevQuarterYear, prevQuarterStartMonth, 1);
    prevEndDate = new Date(prevStartDate);
    prevEndDate.setDate(prevStartDate.getDate() + diffInDays);
  } else if (timeLine?.value === "monthToDate") {
    const prevMonth = start.getMonth() - 1;
    const prevMonthYear =
      prevMonth < 0 ? start.getFullYear() - 1 : start.getFullYear();
    const prevMonthStart = prevMonth < 0 ? 11 : prevMonth;

    prevStartDate = new Date(prevMonthYear, prevMonthStart, 1);
    prevEndDate = new Date(prevStartDate);
    prevEndDate.setDate(prevStartDate.getDate() + diffInDays);
  } else if (timeLine?.value === "yearToDate") {
    const prevYear = start.getFullYear() - 1;
    prevStartDate = new Date(prevYear, 0, 1);
    prevEndDate = new Date(prevStartDate);
    prevEndDate.setDate(prevStartDate.getDate() + diffInDays);
  } else {
    prevEndDate = new Date(start);
    prevEndDate.setDate(prevEndDate.getDate() - 1);
    prevStartDate = new Date(prevEndDate);
    prevStartDate.setDate(prevEndDate.getDate() - diffInDays);
  }

  return {
    startDate: formatDate(prevStartDate),
    endDate: formatDate(prevEndDate),
  };
};

/**
 * Formats a date string into "YYYY-MM-DD" format.
 *
 * @param {string|Date} dateString - The date to format.
 * @returns {string} - The formatted date string.
 */
export const formatDate = (dateString) => {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();
  return `${year}-${month}-${day}`;
};

/**
 * Checks if a given date string represents today's date.
 *
 * @param {string} dateStr - The date string to check.
 * @returns {boolean} - Returns true if the date is today, otherwise false.
 */
export const isToday = (dateStr) => {
  const today = new Date();
  const date = new Date(dateStr);
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
};

export const formatAmount = (amount, isShowFull = false) => {
  if (!amount) return "--";
  const numericAmount = Number(amount);

  if (isNaN(numericAmount) || numericAmount === 0) {
    return "₹0";
  }
  const sign = numericAmount < 0 ? "-" : "";

  // Show full amount with commas when isShowFull is true
  if (isShowFull) {
    return `${sign}₹${numericAmount.toLocaleString("en-IN", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  }

  // if(isMobileScreen){
  //   if (absAmount >= 1_00_000) {
  //     return `${sign}₹${(absAmount / 1_00_000).toFixed(1)} L`;
  //   }
  //   if (absAmount >= 1_000) { // 1,000 or more
  //     return `${sign}₹${(absAmount / 1_000).toFixed(1)} K`;
  //   }
  // }
  // Convert to Crores (Cr) if >= 1,00,00,000 (1 Crore)
  // if (absAmount >= 1_00_00_000) {
  //   return `${sign}₹${(absAmount / 1_00_00_000).toFixed(1)} Cr`;
  // }

  // Default formatting for amounts less than 1 Lakh
  return `₹${numericAmount.toLocaleString("en-IN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

export const formatNumber = (num) => {
  if (num === 0) return "0"; // Handle zero case explicitly
  const absNum = Math.abs(num);
  let suffix = "";

  if (absNum >= 10000000) {
    const result = absNum / 10000000;
    suffix =
      result < 10 ? result.toFixed(2) + " Cr" : result.toFixed(1) + " Cr";
  } else if (absNum >= 100000) {
    const result = absNum / 100000;
    suffix = result < 10 ? result.toFixed(2) + " L" : result.toFixed(1) + " L";
  } else if (absNum >= 1000) {
    const result = absNum / 1000;
    suffix = result < 10 ? result.toFixed(2) + " K" : result.toFixed(1) + " K";
  } else {
    suffix = absNum.toString();
  }

  return num < 0 ? `- ${suffix}` : suffix; // Preserve sign
};

export const formatDateLastUpdate = (dateString, disableTime) => {
  const date = new Date(dateString);

  const options = {
    day: "2-digit",
    month: "short",
    year: "numeric",
  };

  if (!disableTime) {
    Object.assign(options, {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  }

  return date
    .toLocaleString("en-GB", options)
    .replace(",", disableTime ? "" : " at");
};

export const formatDateDayTypes = (dateString) => {
  const date = new Date(dateString);
  const today = new Date();

  if (isNaN(date.getTime())) {
    return dateString || "-"; // Return "-" for invalid dates
  }
  // Reset time for an accurate day difference calculation
  today.setHours(0, 0, 0, 0);
  date.setHours(0, 0, 0, 0);

  const diffTime = today - date;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) return "Today";
  if (diffDays === 1) return "Yesterday";
  if (diffDays <= 30 && diffDays > 0) return `${diffDays} days ago`;

  // Format date as 16-Oct-2024
  const day = date.getDate();
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const month = monthNames[date.getMonth()];
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
};

export const formatDateForMonth = (dateString) => {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "Invalid Date";
  const day = date.getUTCDate();
  const month = date.toLocaleString("en-US", { month: "short" });
  return `${day} ${month}`;
};

export const getValidDate = (dateString, dateFormat) => {
  if (!dateString) return null;

  if (dateString instanceof Date) return dateString;

  try {
    const parsedDate = parse(dateString, dateFormat, new Date());
    if (isValid(parsedDate)) return parsedDate;
    const dateObj = new Date(dateString);
    return isValid(dateObj) ? dateObj : null;
  } catch (error) {
    console.error("Error parsing date:", error);
    return null;
  }
};

// function to format date to DD/MM/YYYY
export const formatDateToDDMMYYYY = (date) => {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, "0");
  const month = String(d.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
};
