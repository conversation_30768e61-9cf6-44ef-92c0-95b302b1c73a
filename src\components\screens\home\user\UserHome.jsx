import React, { useState, useEffect } from "react";
import TicketPendingLists from "./components/TicketPendingLists";
import CashFlowContent from "./components/CashFlowContent";
import ls from "local-storage";

function UserHome({ role }) {
  const [selectedBusiness, setSelectedBusiness] = useState(ls.get("selectedBusiness"));

  useEffect(() => {
    const checkLocalStorage = () => {
      const updatedBusiness = ls.get("selectedBusiness");
      if (JSON.stringify(updatedBusiness) !== JSON.stringify(selectedBusiness)) {
        setSelectedBusiness(updatedBusiness);
      }
    };

    const interval = setInterval(checkLocalStorage, 1000); // Polling every 1 second

    return () => clearInterval(interval);
  }, [selectedBusiness]);

  const features = selectedBusiness?.features || [];
  const isReportsEnabled = features?.some(
    (feature) => feature.name === "Reports" && feature.is_active
  );

  if (role === "business_superuser" && isReportsEnabled) {
    return (
      <>
        <CashFlowContent />
      </>
    );
  }

  return <TicketPendingLists />;
}

export default UserHome;
